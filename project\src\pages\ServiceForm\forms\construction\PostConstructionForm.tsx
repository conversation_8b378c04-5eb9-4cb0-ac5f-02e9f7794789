import React from 'react';
import { useLocation } from 'react-router-dom';
import BrandAlignedPostConstructionForm from '../../../Residential/forms/PostConstruction/BrandAlignedPostConstructionForm';
import CommercialBrandAlignedPostConstructionForm from '../../../Commercial/forms/PostConstruction/BrandAlignedPostConstructionForm';

interface PostConstructionFormProps {
  onBack: () => void;
}

export function PostConstructionForm({ onBack }: PostConstructionFormProps) {
  const location = useLocation();
  
  // Determine service context based on URL path or query parameters
  const isCommercialContext = React.useMemo(() => {
    const path = location.pathname.toLowerCase();
    const searchParams = new URLSearchParams(location.search);
    const context = searchParams.get('context')?.toLowerCase();
    
    // Check if we're in commercial context
    return path.includes('/commercial') || 
           context === 'commercial' || 
           context === 'business' ||
           searchParams.get('category') === 'commercial';
  }, [location]);

  console.log(`🎯 PostConstructionForm: Routing to ${isCommercialContext ? 'Commercial' : 'Residential'} form based on context`);
  
  // Route to appropriate form based on context
  if (isCommercialContext) {
    return <CommercialBrandAlignedPostConstructionForm />;
  } else {
    return <BrandAlignedPostConstructionForm />;
  }
}
