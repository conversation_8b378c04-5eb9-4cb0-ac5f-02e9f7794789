# STATIC CONSTRUCTION FORM ANALYSIS REPORT

**Analysis Date:** 2025-08-06T22:57:47.954Z

## Executive Summary

- **Construction Forms Found:** 0
- **Database Constraints:** 25
- **Service Type Usage Patterns:** 0
- **Potential Issues:** 1
- **Critical Issues:** 0

## Construction Forms Analysis



## Database Constraints Analysis


### 1. 20250105050000_add_waste_management.sql

**Supported Service Types:**
- `office`
- `carpet`
- `window`
- `deep`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `waste-management`
- `post-construction`
- `chimney`
- `pool`
- `upholstery`
- `move-out`
- `event`
- `regular`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 2. 20250120061651_dawn_credit.sql

**Supported Service Types:**
- `office`
- `carpet`
- `window`
- `deep`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 3. 20250120204112_twilight_stream.sql

**Supported Service Types:**
- `office`
- `carpet`
- `window`
- `deep`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 4. 20250121015127_polished_band.sql

**Supported Service Types:**
- `office`
- `carpet`
- `window`
- `deep`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 5. 20250121015555_square_flame.sql

**Supported Service Types:**
- `office`
- `carpet`
- `window`
- `deep`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 6. 20250125191949_black_frost.sql

**Supported Service Types:**
- `office`
- `carpet`
- `window`
- `deep`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 7. 20250202042536_wild_darkness.sql

**Supported Service Types:**
- `office`
- `carpet`
- `window`
- `deep`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 8. 20250203033303_azure_wildflower.sql

**Supported Service Types:**
- `office`
- `carpet`
- `window`
- `deep`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `residential_regular`
- `residential_deep`
- `residential_move`
- `pool`
- `waste-management`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 9. 20250203034500_fix_booking_schema.sql

**Supported Service Types:**
- `office`
- `carpet`
- `window`
- `deep`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `residential_regular`
- `residential_deep`
- `residential_move`
- `pool`
- `waste-management`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 10. 20250206005332_fierce_flower.sql

**Supported Service Types:**
- `regular`
- `deep`
- `move-in-out`
- `post-construction`
- `vacation-rental`
- `event`

**Construction Support:**
- Has 'construction': ❌
- Has 'residential_post_construction': ❌


### 11. 20250421033646_winter_recipe.sql

**Supported Service Types:**
- `regular`
- `deep`
- `move-in-out`
- `post-construction`
- `vacation-rental`
- `event`

**Construction Support:**
- Has 'construction': ❌
- Has 'residential_post_construction': ❌


### 12. 20250709000000_fix_service_types.sql

**Supported Service Types:**
- `office`
- `carpet`
- `window`
- `deep`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `residential_regular`
- `residential_deep`
- `residential_move`
- `pool`
- `waste-management`
- `residential`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 13. 20250709000001_fix_payment_system_schema.sql

**Supported Service Types:**
- `office`
- `carpet`
- `window`
- `deep`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `pool`
- `chimney`
- `waste-management`
- `upholstery`
- `move-out`
- `event`
- `post-construction`
- `residential`
- `residential_regular`
- `residential_deep`
- `residential_move`
- `regular`
- `Regular House Cleaning`
- `Deep House Cleaning`
- `Move-in/Move-out Cleaning`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 14. 20250711000000_standardize_service_types.sql

**Supported Service Types:**
- `residential_regular`
- `residential_deep`
- `residential_move`
- `residential`
- `residential_post_construction`
- `office`
- `carpet`
- `window`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `pool`
- `chimney`
- `waste-management`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ✅


### 15. 20250711000000_standardize_service_types.sql

**Supported Service Types:**
- `residential_regular`
- `residential_deep`
- `residential_move`
- `residential`
- `office`
- `carpet`
- `window`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `pool`
- `chimney`
- `waste-management`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 16. 20250711000001_fix_critical_schema_inconsistencies.sql

**Supported Service Types:**
- `residential_regular`
- `residential_deep`
- `residential_move`
- `residential`
- `office`
- `carpet`
- `window`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `pool`
- `chimney`
- `waste-management`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 17. 20250711000001_fix_critical_schema_inconsistencies.sql

**Supported Service Types:**
- `residential_regular`
- `residential_deep`
- `residential_move`
- `residential`
- `office`
- `carpet`
- `window`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `pool`
- `chimney`
- `waste-management`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 18. 20250715000000_fix_payment_system_final.sql

**Supported Service Types:**
- `residential_regular`
- `residential_deep`
- `residential_move`
- `residential`
- `office`
- `carpet`
- `window`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `pool`
- `chimney`
- `waste-management`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 19. 20250715000000_fix_payment_system_final.sql

**Supported Service Types:**
- `residential_regular`
- `residential_deep`
- `residential_move`
- `residential`
- `office`
- `carpet`
- `window`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `pool`
- `chimney`
- `waste-management`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ❌


### 20. 20250729000000_fix_residential_post_construction.sql

**Supported Service Types:**
- `residential_regular`
- `residential_deep`
- `residential_move`
- `residential`
- `residential_post_construction`
- `office`
- `carpet`
- `window`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `pool`
- `chimney`
- `waste-management`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ✅


### 21. 20250729000000_fix_residential_post_construction.sql

**Supported Service Types:**
- `residential_regular`
- `residential_deep`
- `residential_move`
- `residential`
- `residential_post_construction`
- `office`
- `carpet`
- `window`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `pool`
- `chimney`
- `waste-management`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ✅


### 22. 20250805000000_add_industrial_service_type.sql

**Supported Service Types:**
- `residential_regular`
- `residential_deep`
- `residential_move`
- `residential`
- `residential_post_construction`
- `residential_event`
- `residential_upholstery`
- `office`
- `carpet`
- `window`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `pool`
- `chimney`
- `waste-management`
- `industrial`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ✅


### 23. 20250805000000_add_industrial_service_type.sql

**Supported Service Types:**
- `residential_regular`
- `residential_deep`
- `residential_move`
- `residential`
- `residential_post_construction`
- `residential_event`
- `residential_upholstery`
- `office`
- `carpet`
- `window`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `pool`
- `chimney`
- `waste-management`
- `industrial`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ✅


### 24. 20250806000000_unified_post_construction_service_types.sql

**Supported Service Types:**
- `residential_regular`
- `residential_deep`
- `residential_move`
- `residential`
- `residential_post_construction`
- `residential_event`
- `residential_upholstery`
- `office`
- `carpet`
- `window`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `pool`
- `chimney`
- `waste-management`
- `industrial`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ✅


### 25. 20250806000000_unified_post_construction_service_types.sql

**Supported Service Types:**
- `residential_regular`
- `residential_deep`
- `residential_move`
- `residential`
- `residential_post_construction`
- `residential_event`
- `residential_upholstery`
- `office`
- `carpet`
- `window`
- `construction`
- `sanitization`
- `tile`
- `pressure`
- `floor`
- `pool`
- `chimney`
- `waste-management`
- `industrial`

**Construction Support:**
- Has 'construction': ✅
- Has 'residential_post_construction': ✅


## Service Type Usage Patterns



## Potential Issues


### 1. Database supports construction service types but no forms found (WARNING)

**Description:** Forms may not be properly connected or named


## Recommendations


### 1. Add comprehensive testing (LOW Priority)

**Description:** Implement end-to-end testing for construction form submissions

**Steps:**
1. Create automated tests for each construction form type
1. Test database insertion with various service types
1. Monitor form submission success rates


## Key Findings Summary

Based on this static analysis:

1. **Form-Database Alignment:** Forms appear aligned with database constraints

2. **Service Type Consistency:** Service types appear consistent

3. **Implementation Completeness:** Forms appear to have submission handling

## Next Steps

1. **Immediate Actions:** Address critical issues related to database constraints
2. **Code Review:** Review flagged forms for proper service type handling
3. **Testing:** Run end-to-end tests to verify form submission works
4. **Database Verification:** Check actual database state to confirm data storage

---
*This report was generated by static code analysis*
*For complete verification, database connectivity and runtime testing are recommended*
