/**
 * Enhanced Booking Management Service
 * 
 * Provides comprehensive booking management including:
 * - Advanced CRUD operations
 * - Status management
 * - Conflict resolution
 * - Bulk operations
 */

import { supabase } from '../supabase/client';
import { StandardizedBookingData } from '../api/bookingService';
import { BookingValidationService, ValidationError, ConflictCheck } from './bookingValidationService';
import { ErrorHandler } from '../utils/errorHandler';

export interface BookingSearchFilters {
  status?: string[];
  serviceType?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  paymentStatus?: string[];
  customerEmail?: string;
  propertyAddress?: string;
  priceRange?: {
    min: number;
    max: number;
  };
}

export interface BookingStatusTransition {
  bookingId: string;
  fromStatus: string;
  toStatus: string;
  reason: string;
  timestamp: string;
  updatedBy: string;
  metadata?: Record<string, any>;
}

export interface BulkOperationResult {
  successCount: number;
  failureCount: number;
  errors: Array<{
    bookingId: string;
    error: string;
  }>;
}

export interface BookingConflictResolution {
  conflictType: string;
  resolution: 'reschedule' | 'cancel' | 'override' | 'merge';
  newSchedule?: any;
  reason: string;
}

export class EnhancedBookingService {

  /**
   * Create a new booking with comprehensive validation
   */
  static async createBooking(
    bookingData: Partial<StandardizedBookingData>,
    options: {
      validateConflicts?: boolean;
      autoResolveConflicts?: boolean;
      userId?: string;
    } = {}
  ): Promise<{
    success: boolean;
    booking?: StandardizedBookingData;
    errors?: ValidationError[];
    conflicts?: ConflictCheck[];
    warnings?: ValidationError[];
  }> {
    try {
      // 1. Comprehensive validation
      const validation = await BookingValidationService.validateBooking(
        bookingData,
        bookingData.service_type || ''
      );

      if (!validation.isValid) {
        return {
          success: false,
          errors: validation.errors,
          warnings: validation.warnings,
          conflicts: validation.conflicts
        };
      }

      // 2. Handle conflicts if any
      if (validation.conflicts.length > 0 && options.validateConflicts !== false) {
        if (options.autoResolveConflicts) {
          const resolution = await this.resolveConflicts(bookingData, validation.conflicts);
          if (!resolution.success) {
            return {
              success: false,
              errors: validation.errors,
              conflicts: validation.conflicts,
              warnings: validation.warnings
            };
          }
          // Apply resolved data
          Object.assign(bookingData, resolution.resolvedData);
        } else {
          return {
            success: false,
            errors: validation.errors,
            conflicts: validation.conflicts,
            warnings: validation.warnings
          };
        }
      }

      // 3. Prepare final booking data
      const finalBookingData = {
        ...bookingData,
        user_id: options.userId || bookingData.user_id,
        status: bookingData.status || 'pending',
        payment_status: bookingData.payment_status || 'pending',
        metadata: {
          ...bookingData.metadata,
          submittedAt: new Date().toISOString(),
          validatedAt: new Date().toISOString(),
          conflictsResolved: validation.conflicts.length > 0 && options.autoResolveConflicts
        }
      };

      // 4. Save to database
      if (!supabase) {
        throw new Error('Database connection not available');
      }

      const { data, error } = await supabase
        .from('booking_forms')
        .insert([finalBookingData])
        .select()
        .single();

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      // 5. Log booking creation
      await this.logBookingActivity(data.id, 'created', 'Booking created successfully', options.userId);

      return {
        success: true,
        booking: data,
        warnings: validation.warnings
      };

    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError(
        'BOOKING_CREATION_ERROR',
        'Failed to create booking',
        error
      ));

      return {
        success: false,
        errors: [{
          field: 'general',
          message: error instanceof Error ? error.message : 'Failed to create booking',
          code: 'CREATION_ERROR',
          severity: 'error'
        }]
      };
    }
  }

  /**
   * Update booking with validation and conflict checking
   */
  static async updateBooking(
    bookingId: string,
    updates: Partial<StandardizedBookingData>,
    options: {
      validateChanges?: boolean;
      userId?: string;
      reason?: string;
    } = {}
  ): Promise<{
    success: boolean;
    booking?: StandardizedBookingData;
    errors?: ValidationError[];
    conflicts?: ConflictCheck[];
  }> {
    try {
      if (!supabase) {
        throw new Error('Database connection not available');
      }

      // 1. Get current booking
      const { data: currentBooking, error: fetchError } = await supabase
        .from('booking_forms')
        .select('*')
        .eq('id', bookingId)
        .single();

      if (fetchError || !currentBooking) {
        throw new Error('Booking not found');
      }

      // 2. Merge updates with current data
      const updatedData = { ...currentBooking, ...updates };

      // 3. Validate if requested
      if (options.validateChanges !== false) {
        const validation = await BookingValidationService.validateBooking(
          updatedData,
          updatedData.service_type
        );

        if (!validation.isValid) {
          return {
            success: false,
            errors: validation.errors,
            conflicts: validation.conflicts
          };
        }
      }

      // 4. Update in database
      const { data, error } = await supabase
        .from('booking_forms')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', bookingId)
        .select()
        .single();

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      // 5. Log update activity
      await this.logBookingActivity(
        bookingId,
        'updated',
        options.reason || 'Booking updated',
        options.userId,
        { updates }
      );

      return {
        success: true,
        booking: data
      };

    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError(
        'BOOKING_UPDATE_ERROR',
        'Failed to update booking',
        error
      ));

      return {
        success: false,
        errors: [{
          field: 'general',
          message: error instanceof Error ? error.message : 'Failed to update booking',
          code: 'UPDATE_ERROR',
          severity: 'error'
        }]
      };
    }
  }

  /**
   * Update booking status with transition validation
   */
  static async updateBookingStatus(
    bookingId: string,
    newStatus: StandardizedBookingData['status'],
    options: {
      reason?: string;
      userId?: string;
      validateTransition?: boolean;
    } = {}
  ): Promise<{
    success: boolean;
    booking?: StandardizedBookingData;
    error?: string;
  }> {
    try {
      if (!supabase) {
        throw new Error('Database connection not available');
      }

      // 1. Get current booking
      const { data: currentBooking, error: fetchError } = await supabase
        .from('booking_forms')
        .select('*')
        .eq('id', bookingId)
        .single();

      if (fetchError || !currentBooking) {
        throw new Error('Booking not found');
      }

      // 2. Validate status transition
      if (options.validateTransition !== false) {
        const isValidTransition = this.isValidStatusTransition(
          currentBooking.status,
          newStatus
        );

        if (!isValidTransition) {
          throw new Error(`Invalid status transition from ${currentBooking.status} to ${newStatus}`);
        }
      }

      // 3. Update status
      const { data, error } = await supabase
        .from('booking_forms')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', bookingId)
        .select()
        .single();

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      // 4. Log status transition
      await this.logBookingActivity(
        bookingId,
        'status_changed',
        options.reason || `Status changed to ${newStatus}`,
        options.userId,
        {
          fromStatus: currentBooking.status,
          toStatus: newStatus
        }
      );

      return {
        success: true,
        booking: data
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update status'
      };
    }
  }

  /**
   * Search bookings with advanced filtering
   */
  static async searchBookings(
    filters: BookingSearchFilters,
    options: {
      limit?: number;
      offset?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      userId?: string;
    } = {}
  ): Promise<{
    bookings: StandardizedBookingData[];
    totalCount: number;
    hasMore: boolean;
  }> {
    try {
      if (!supabase) {
        throw new Error('Database connection not available');
      }

      let query = supabase
        .from('booking_forms')
        .select('*', { count: 'exact' });

      // Apply filters
      if (options.userId) {
        query = query.eq('user_id', options.userId);
      }

      if (filters.status && filters.status.length > 0) {
        query = query.in('status', filters.status);
      }

      if (filters.serviceType && filters.serviceType.length > 0) {
        query = query.in('service_type', filters.serviceType);
      }

      if (filters.paymentStatus && filters.paymentStatus.length > 0) {
        query = query.in('payment_status', filters.paymentStatus);
      }

      if (filters.customerEmail) {
        query = query.ilike('contact->>email', `%${filters.customerEmail}%`);
      }

      if (filters.propertyAddress) {
        query = query.ilike('property_details->>address', `%${filters.propertyAddress}%`);
      }

      if (filters.dateRange) {
        query = query
          .gte('created_at', filters.dateRange.start)
          .lte('created_at', filters.dateRange.end);
      }

      if (filters.priceRange) {
        query = query
          .gte('total_price', filters.priceRange.min)
          .lte('total_price', filters.priceRange.max);
      }

      // Apply sorting
      const sortBy = options.sortBy || 'created_at';
      const sortOrder = options.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
      }
      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 50) - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        throw new Error(`Search error: ${error.message}`);
      }

      const totalCount = count || 0;
      const currentCount = (options.offset || 0) + (data?.length || 0);

      return {
        bookings: data || [],
        totalCount,
        hasMore: currentCount < totalCount
      };

    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError(
        'BOOKING_SEARCH_ERROR',
        'Failed to search bookings',
        error
      ));

      return {
        bookings: [],
        totalCount: 0,
        hasMore: false
      };
    }
  }

  /**
   * Bulk operations on multiple bookings
   */
  static async bulkUpdateStatus(
    bookingIds: string[],
    newStatus: StandardizedBookingData['status'],
    options: {
      reason?: string;
      userId?: string;
    } = {}
  ): Promise<BulkOperationResult> {
    const result: BulkOperationResult = {
      successCount: 0,
      failureCount: 0,
      errors: []
    };

    for (const bookingId of bookingIds) {
      try {
        const updateResult = await this.updateBookingStatus(bookingId, newStatus, options);
        
        if (updateResult.success) {
          result.successCount++;
        } else {
          result.failureCount++;
          result.errors.push({
            bookingId,
            error: updateResult.error || 'Unknown error'
          });
        }
      } catch (error) {
        result.failureCount++;
        result.errors.push({
          bookingId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return result;
  }

  /**
   * Resolve booking conflicts
   */
  private static async resolveConflicts(
    bookingData: Partial<StandardizedBookingData>,
    conflicts: ConflictCheck[]
  ): Promise<{
    success: boolean;
    resolvedData?: Partial<StandardizedBookingData>;
    resolution?: BookingConflictResolution;
  }> {
    // Simple conflict resolution strategy
    // In a real implementation, this would be more sophisticated
    
    for (const conflict of conflicts) {
      if (conflict.type === 'time_overlap') {
        // Suggest alternative time slots
        const alternativeTime = this.suggestAlternativeTime(bookingData.schedule?.preferredTime);
        if (alternativeTime) {
          return {
            success: true,
            resolvedData: {
              ...bookingData,
              schedule: {
                ...bookingData.schedule,
                preferredTime: alternativeTime
              }
            },
            resolution: {
              conflictType: conflict.type,
              resolution: 'reschedule',
              newSchedule: { preferredTime: alternativeTime },
              reason: 'Automatically rescheduled to avoid conflict'
            }
          };
        }
      }
    }

    return { success: false };
  }

  /**
   * Suggest alternative time slots
   */
  private static suggestAlternativeTime(currentTime?: string): string | null {
    const timeSlots = ['morning', 'afternoon', 'evening'];
    const currentIndex = timeSlots.indexOf(currentTime || '');
    
    if (currentIndex !== -1 && currentIndex < timeSlots.length - 1) {
      return timeSlots[currentIndex + 1];
    }
    
    return timeSlots[0];
  }

  /**
   * Validate status transitions
   */
  private static isValidStatusTransition(
    fromStatus: string,
    toStatus: string
  ): boolean {
    const validTransitions: Record<string, string[]> = {
      'pending': ['confirmed', 'cancelled'],
      'confirmed': ['in_progress', 'cancelled', 'rescheduled'],
      'in_progress': ['completed', 'cancelled'],
      'completed': [], // Terminal state
      'cancelled': ['pending'], // Can be reactivated
      'rescheduled': ['confirmed', 'cancelled']
    };

    return validTransitions[fromStatus]?.includes(toStatus) || false;
  }

  /**
   * Log booking activity
   */
  private static async logBookingActivity(
    bookingId: string,
    activityType: string,
    description: string,
    userId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      if (!supabase) return;

      await supabase
        .from('booking_activities')
        .insert([{
          booking_id: bookingId,
          activity_type: activityType,
          description,
          performed_by: userId,
          performed_at: new Date().toISOString(),
          metadata: metadata || {}
        }]);

    } catch (error) {
      console.error('Failed to log booking activity:', error);
    }
  }

  /**
   * Get booking statistics
   */
  static async getBookingStatistics(
    userId?: string,
    dateRange?: { start: string; end: string }
  ): Promise<{
    totalBookings: number;
    bookingsByStatus: Record<string, number>;
    bookingsByService: Record<string, number>;
    revenueByMonth: Record<string, number>;
    averageBookingValue: number;
  }> {
    try {
      if (!supabase) {
        throw new Error('Database connection not available');
      }

      let query = supabase.from('booking_forms').select('*');

      if (userId) {
        query = query.eq('user_id', userId);
      }

      if (dateRange) {
        query = query
          .gte('created_at', dateRange.start)
          .lte('created_at', dateRange.end);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Statistics error: ${error.message}`);
      }

      const bookings = data || [];

      // Calculate statistics
      const totalBookings = bookings.length;
      const bookingsByStatus: Record<string, number> = {};
      const bookingsByService: Record<string, number> = {};
      const revenueByMonth: Record<string, number> = {};
      let totalRevenue = 0;

      bookings.forEach(booking => {
        // Status distribution
        bookingsByStatus[booking.status] = (bookingsByStatus[booking.status] || 0) + 1;

        // Service type distribution
        bookingsByService[booking.service_type] = (bookingsByService[booking.service_type] || 0) + 1;

        // Revenue by month
        if (booking.total_price) {
          const month = new Date(booking.created_at).toISOString().substring(0, 7);
          revenueByMonth[month] = (revenueByMonth[month] || 0) + Number(booking.total_price);
          totalRevenue += Number(booking.total_price);
        }
      });

      const averageBookingValue = totalBookings > 0 ? totalRevenue / totalBookings : 0;

      return {
        totalBookings,
        bookingsByStatus,
        bookingsByService,
        revenueByMonth,
        averageBookingValue
      };

    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError(
        'STATISTICS_ERROR',
        'Failed to calculate booking statistics',
        error
      ));

      return {
        totalBookings: 0,
        bookingsByStatus: {},
        bookingsByService: {},
        revenueByMonth: {},
        averageBookingValue: 0
      };
    }
  }
}
