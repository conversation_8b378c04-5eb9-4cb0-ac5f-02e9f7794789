import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Building, Sparkles, CheckCircle, 
  ArrowRight, HardHat, Users, Mail, Phone, Home, User, 
  Calendar, Layers, Hammer, Paintbrush, Wrench, Building2
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { AnimatedBackground } from '../../../components/layout/AnimatedBackground';
import { useAuth } from '../../../lib/auth/AuthProvider';
import GlassmorphismSelect from '../../../components/ui/GlassmorphismSelect';

interface PostConstructionFormData {
  projectType: string;
  projectSize: string;
  serviceFrequency: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  addOns: string[];
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
}

const BrandAlignedPostConstructionForm: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<PostConstructionFormData>>({
    projectType: 'commercial',
    projectSize: 'medium',
    serviceFrequency: 'one-time',
    addOns: [],
  });
  
  useEffect(() => {
    const savedData = localStorage.getItem('postConstructionFormData');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('postConstructionFormData', JSON.stringify(formData));
  }, [formData]);

  const steps = [
    { id: 1, name: 'Project Type' },
    { id: 2, name: 'Details & Schedule' },
    { id: 3, name: 'Add-ons' },
    { id: 4, name: 'Contact' },
  ];

  const projectTypes = [
    { id: 'commercial', name: 'Commercial Construction', icon: <Building className="w-8 h-8" />, description: 'Office buildings and commercial spaces' },
    { id: 'residential', name: 'Residential Construction', icon: <Home className="w-8 h-8" />, description: 'Homes and residential properties' },
    { id: 'industrial', name: 'Industrial Project', icon: <HardHat className="w-8 h-8" />, description: 'Warehouses and industrial facilities' },
    { id: 'renovation', name: 'Renovation Project', icon: <Hammer className="w-8 h-8" />, description: 'Remodel and renovation cleanup' },
  ];

  const projectSizeOptions = [
    { id: 'small', name: 'Under 2,500 sq ft' },
    { id: 'medium', name: '2,500 - 10,000 sq ft' },
    { id: 'large', name: '10,000 - 50,000 sq ft' },
    { id: 'xl', name: '50,000+ sq ft' },
  ];
  
  const serviceFrequencies = [
    { id: 'one-time', name: 'One-Time Cleanup' },
    { id: 'phase-based', name: 'Phase-Based' },
    { id: 'weekly', name: 'Weekly During Construction' },
    { id: 'final-only', name: 'Final Cleanup Only' },
  ];

  const addOnServices = [
    { id: 'debris-removal', name: 'Construction Debris Removal' },
    { id: 'window-cleaning', name: 'Window & Glass Cleaning' },
    { id: 'floor-polishing', name: 'Floor Polishing & Sealing' },
    { id: 'hvac-cleaning', name: 'HVAC System Cleaning' },
  ];

  const timeSlots = [
    { id: 'morning', name: 'Morning (8AM - 12PM)' },
    { id: 'afternoon', name: 'Afternoon (1PM - 5PM)' },
    { id: 'evening', name: 'Evening (5PM - 9PM)' },
    { id: 'weekend', name: 'Weekend (Sat/Sun)' },
  ];
  
  const handleAddOnToggle = (addOnId: string) => {
    const currentAddOns = formData.addOns || [];
    if (currentAddOns.includes(addOnId)) {
      setFormData({
        ...formData,
        addOns: currentAddOns.filter(id => id !== addOnId)
      });
    } else {
      setFormData({
        ...formData,
        addOns: [...currentAddOns, addOnId]
      });
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">
              Post-Construction Cleaning
            </h1>
            <p className="text-gray-600">Professional cleanup services for your construction project.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${currentStep >= step.id ? 'bg-emerald-800 text-white shadow-md' : 'bg-white border-2 border-gray-200 text-gray-600'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} className="text-white" style={{ color: 'white' }} /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-gray-900 font-medium' : 'text-gray-500'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-emerald-800 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <motion.div className="bg-white border border-gray-200 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-200" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
            <AnimatePresence mode="wait">
              {currentStep === 1 && (
                <motion.div key="step1">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Select Project Type</h2>
                   <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {projectTypes.map(type => (
                      <motion.button 
                        key={type.id} 
                        onClick={() => setFormData({...formData, projectType: type.id})} 
                        whileHover={{ scale: 1.02, y: -2 }}
                        whileTap={{ scale: 0.98 }}
                        className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${formData.projectType === type.id ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'}`}
                      >
                        <div className="flex items-center gap-4">
                          <div className={`p-2 rounded-lg transition-colors duration-200 ${formData.projectType === type.id ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'}`}>
                            {type.icon}
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">{type.name}</h3>
                            <p className="text-sm text-gray-600">{type.description}</p>
                          </div>
                        </div>
                      </motion.button>
                    ))}
                  </div>
                  <div className="flex justify-end">
                    <Button 
                      onClick={() => setCurrentStep(2)} 
                      disabled={!formData.projectType}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}
               {currentStep === 2 && (
                <motion.div key="step2">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Project Details & Schedule</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                     <GlassmorphismSelect
                        options={projectSizeOptions}
                        value={formData.projectSize}
                        onChange={value => setFormData({...formData, projectSize: value})}
                        placeholder="Select Project Size"
                      />
                     <GlassmorphismSelect
                        options={serviceFrequencies}
                        value={formData.serviceFrequency}
                        onChange={value => setFormData({...formData, serviceFrequency: value})}
                        placeholder="Select Service Type"
                      />
                     <input 
                       type="date" 
                       value={formData.preferredDate || ''} 
                       onChange={e => setFormData({...formData, preferredDate: e.target.value})} 
                       className="w-full bg-white border border-gray-200 rounded-xl p-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" 
                     />
                     <GlassmorphismSelect
                        options={timeSlots}
                        value={formData.preferredTime}
                        onChange={value => setFormData({...formData, preferredTime: value})}
                        placeholder="Select Time"
                      />
                  </div>
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(3)} 
                      disabled={!formData.projectSize || !formData.serviceFrequency || !formData.preferredDate || !formData.preferredTime}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                </motion.div>
              )}
              {currentStep === 3 && (
                <motion.div key="step3">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Specialized Services</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {addOnServices.map(addon => (
                      <motion.button 
                        key={addon.id} 
                        onClick={() => handleAddOnToggle(addon.id)} 
                        whileHover={{ scale: 1.02, y: -2 }}
                        whileTap={{ scale: 0.98 }}
                        className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${(formData.addOns || []).includes(addon.id) ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'}`}
                      >
                        <div className="flex items-center justify-between">
                          <span className="font-medium text-gray-900">{addon.name}</span>
                          {(formData.addOns || []).includes(addon.id) && (
                            <CheckCircle className="w-5 h-5 text-emerald-600" />
                          )}
                        </div>
                      </motion.button>
                    ))}
                  </div>
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(4)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}
              {currentStep === 4 && (
                <motion.div key="step4">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Contact Information</h2>
                   <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input type="text" placeholder="Company Name" value={formData.companyName || ''} onChange={e => setFormData({...formData, companyName: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 pl-12 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" />
                    </div>
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input type="text" placeholder="Contact Name" value={formData.contactName || ''} onChange={e => setFormData({...formData, contactName: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 pl-12 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" />
                    </div>
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input type="email" placeholder="Email" value={formData.email || ''} onChange={e => setFormData({...formData, email: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 pl-12 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" />
                    </div>
                    <div className="relative">
                      <Phone className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input type="tel" placeholder="Phone" value={formData.phone || ''} onChange={e => setFormData({...formData, phone: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 pl-12 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" />
                    </div>
                    <div className="relative sm:col-span-2">
                      <Home className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input type="text" placeholder="Project Address" value={formData.address || ''} onChange={e => setFormData({...formData, address: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 pl-12 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" />
                    </div>
                    <input type="text" placeholder="City" value={formData.city || ''} onChange={e => setFormData({...formData, city: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" />
                    <input type="text" placeholder="ZIP Code" value={formData.zipCode || ''} onChange={e => setFormData({...formData, zipCode: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" />
                  </div>
                  <textarea placeholder="Project Details & Special Instructions" value={formData.specialInstructions || ''} onChange={e => setFormData({...formData, specialInstructions: e.target.value})} className="w-full bg-white border border-gray-200 rounded-xl p-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" rows={4} />
                  <div className="flex justify-between mt-6">
                    <Button variant="outline" onClick={() => setCurrentStep(3)}>Back</Button>
                    <Button 
                      onClick={() => navigate('/commercial/thank-you') }
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105"
                    >
                      Submit Request
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>
    </AnimatedBackground>
  );
};

export default BrandAlignedPostConstructionForm; 