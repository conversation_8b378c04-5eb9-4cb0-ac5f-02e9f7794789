#!/usr/bin/env node

/**
 * DATABASE CONSTRUCTION FORM INVESTIGATION SCRIPT
 * 
 * CRITICAL INVESTIGATION: Verify if construction service form data is being stored 
 * in the database and examine the root cause of retrieval/display issues.
 * 
 * This script performs direct database queries to:
 * 1. Examine recent database entries for construction bookings
 * 2. Analyze service_type values stored for construction forms
 * 3. Validate JSONB field data structure
 * 4. Check user_id associations and timestamps
 * 5. Assess migration impact on existing data
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Configuration
const INVESTIGATION_CONFIG = {
  outputFile: path.join(__dirname, '../CONSTRUCTION_DATABASE_INVESTIGATION_RESULTS.md'),
  queryTimeout: 30000, // 30 seconds
  anonymizeData: true
};

// Get Supabase configuration from environment
function getSupabaseConfig() {
  const envFile = path.join(__dirname, '../.env');
  let config = {
    url: process.env.VITE_SUPABASE_URL,
    anonKey: process.env.VITE_SUPABASE_ANON_KEY
  };

  // Try to read from .env file if environment variables are not set
  if (!config.url || !config.anonKey) {
    try {
      if (fs.existsSync(envFile)) {
        const envContent = fs.readFileSync(envFile, 'utf8');
        const envLines = envContent.split('\n');
        
        for (const line of envLines) {
          const [key, value] = line.split('=');
          if (key && value) {
            if (key.trim() === 'VITE_SUPABASE_URL') {
              config.url = value.trim().replace(/['"]/g, '');
            } else if (key.trim() === 'VITE_SUPABASE_ANON_KEY') {
              config.anonKey = value.trim().replace(/['"]/g, '');
            }
          }
        }
      }
    } catch (error) {
      console.warn('Could not read .env file:', error.message);
    }
  }

  return config;
}

// Initialize Supabase client
function initSupabaseClient() {
  const config = getSupabaseConfig();
  
  if (!config.url || !config.anonKey) {
    throw new Error('Supabase configuration missing. Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY');
  }

  return createClient(config.url, config.anonKey);
}

// Anonymize sensitive data
function anonymizeData(data) {
  if (!INVESTIGATION_CONFIG.anonymizeData) return data;
  
  const anonymized = { ...data };
  
  // Anonymize contact information
  if (anonymized.contact) {
    if (anonymized.contact.email) {
      anonymized.contact.email = '<EMAIL>';
    }
    if (anonymized.contact.phone) {
      anonymized.contact.phone = '555-0000';
    }
    if (anonymized.contact.firstName) {
      anonymized.contact.firstName = 'John';
    }
    if (anonymized.contact.lastName) {
      anonymized.contact.lastName = 'Doe';
    }
  }
  
  // Anonymize property details
  if (anonymized.property_details) {
    if (anonymized.property_details.address) {
      anonymized.property_details.address = '123 Main St';
    }
    if (anonymized.property_details.city) {
      anonymized.property_details.city = 'Anytown';
    }
  }
  
  // Anonymize user_id
  if (anonymized.user_id) {
    anonymized.user_id = 'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx';
  }
  
  return anonymized;
}

// Investigation queries
const INVESTIGATION_QUERIES = {
  // 1. Get all construction-related bookings
  constructionBookings: `
    SELECT 
      id, service_type, status, created_at, updated_at,
      property_details, service_details, schedule, contact,
      total_price, special_instructions
    FROM booking_forms 
    WHERE service_type LIKE '%construction%' 
       OR service_type = 'construction'
    ORDER BY created_at DESC
    LIMIT 50
  `,
  
  // 2. Count bookings by service type
  serviceTypeCounts: `
    SELECT 
      service_type,
      COUNT(*) as count,
      MIN(created_at) as first_booking,
      MAX(created_at) as latest_booking
    FROM booking_forms 
    GROUP BY service_type 
    ORDER BY count DESC
  `,
  
  // 3. Recent bookings in last 30 days
  recentBookings: `
    SELECT 
      id, service_type, status, created_at,
      CASE 
        WHEN property_details IS NOT NULL THEN 'Has Property Details'
        ELSE 'Missing Property Details'
      END as property_status,
      CASE 
        WHEN service_details IS NOT NULL THEN 'Has Service Details'
        ELSE 'Missing Service Details'
      END as service_status,
      CASE 
        WHEN contact IS NOT NULL THEN 'Has Contact Info'
        ELSE 'Missing Contact Info'
      END as contact_status
    FROM booking_forms 
    WHERE created_at >= NOW() - INTERVAL '30 days'
    ORDER BY created_at DESC
  `,
  
  // 4. Check for data quality issues
  dataQualityCheck: `
    SELECT 
      service_type,
      COUNT(*) as total_records,
      COUNT(CASE WHEN property_details IS NULL THEN 1 END) as missing_property_details,
      COUNT(CASE WHEN service_details IS NULL THEN 1 END) as missing_service_details,
      COUNT(CASE WHEN contact IS NULL THEN 1 END) as missing_contact,
      COUNT(CASE WHEN schedule IS NULL THEN 1 END) as missing_schedule
    FROM booking_forms 
    GROUP BY service_type
    ORDER BY total_records DESC
  `,
  
  // 5. Sample construction booking details
  sampleConstructionData: `
    SELECT 
      id, service_type, created_at,
      property_details, service_details, contact
    FROM booking_forms 
    WHERE service_type IN ('construction', 'residential_post_construction')
    ORDER BY created_at DESC
    LIMIT 3
  `
};

// Execute investigation
async function runInvestigation() {
  console.log('🔍 STARTING DATABASE CONSTRUCTION FORM INVESTIGATION...\n');
  
  const results = {
    timestamp: new Date().toISOString(),
    investigation_summary: {},
    query_results: {},
    findings: [],
    recommendations: []
  };
  
  try {
    const supabase = initSupabaseClient();
    console.log('✅ Supabase client initialized successfully');
    
    // Test connection
    const { error: connectionError } = await supabase
      .from('booking_forms')
      .select('count', { count: 'exact', head: true });
      
    if (connectionError) {
      throw new Error(`Connection failed: ${connectionError.message}`);
    }
    
    console.log('✅ Database connection verified\n');
    
    // Execute all investigation queries
    for (const [queryName, query] of Object.entries(INVESTIGATION_QUERIES)) {
      console.log(`📊 Executing: ${queryName}...`);
      
      try {
        const { data, error } = await supabase.rpc('execute_sql', { 
          sql_query: query 
        }).catch(async () => {
          // Fallback to using appropriate table queries if RPC is not available
          if (queryName === 'constructionBookings') {
            return await supabase
              .from('booking_forms')
              .select('id, service_type, status, created_at, updated_at, property_details, service_details, schedule, contact, total_price, special_instructions')
              .or('service_type.like.%construction%,service_type.eq.construction')
              .order('created_at', { ascending: false })
              .limit(50);
          } else if (queryName === 'recentBookings') {
            return await supabase
              .from('booking_forms')
              .select('id, service_type, status, created_at, property_details, service_details, contact')
              .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
              .order('created_at', { ascending: false });
          } else if (queryName === 'sampleConstructionData') {
            return await supabase
              .from('booking_forms')
              .select('id, service_type, created_at, property_details, service_details, contact')
              .in('service_type', ['construction', 'residential_post_construction'])
              .order('created_at', { ascending: false })
              .limit(3);
          }
          
          throw new Error('RPC function not available and no fallback implemented');
        });
        
        if (error) {
          console.error(`❌ Query ${queryName} failed:`, error.message);
          results.query_results[queryName] = { error: error.message };
          continue;
        }
        
        // Anonymize data if needed
        const processedData = Array.isArray(data) 
          ? data.map(row => anonymizeData(row))
          : anonymizeData(data);
        
        results.query_results[queryName] = {
          success: true,
          row_count: Array.isArray(processedData) ? processedData.length : 1,
          data: processedData
        };
        
        console.log(`✅ Query ${queryName} completed: ${results.query_results[queryName].row_count} rows`);
        
      } catch (queryError) {
        console.error(`❌ Query ${queryName} failed:`, queryError.message);
        results.query_results[queryName] = { error: queryError.message };
      }
    }
    
    // Analyze results and generate findings
    results.findings = analyzeResults(results.query_results);
    results.recommendations = generateRecommendations(results.findings);
    
    // Generate summary
    results.investigation_summary = {
      queries_executed: Object.keys(INVESTIGATION_QUERIES).length,
      successful_queries: Object.values(results.query_results).filter(r => r.success).length,
      failed_queries: Object.values(results.query_results).filter(r => r.error).length,
      total_findings: results.findings.length,
      critical_issues: results.findings.filter(f => f.severity === 'critical').length
    };
    
  } catch (error) {
    console.error('❌ Investigation failed:', error.message);
    results.investigation_summary.error = error.message;
  }
  
  // Write results to file
  await writeInvestigationReport(results);
  
  console.log('\n🎯 INVESTIGATION COMPLETE!');
  console.log(`📋 Report written to: ${INVESTIGATION_CONFIG.outputFile}`);
  
  return results;
}

// Analyze query results
function analyzeResults(queryResults) {
  const findings = [];
  
  // Analyze construction bookings
  if (queryResults.constructionBookings?.success) {
    const constructionData = queryResults.constructionBookings.data || [];
    
    if (constructionData.length === 0) {
      findings.push({
        severity: 'critical',
        category: 'data_availability',
        issue: 'No construction bookings found in database',
        description: 'The database contains no records with construction-related service types.',
        impact: 'Users reporting missing construction bookings may be correct - data is not being stored.'
      });
    } else {
      findings.push({
        severity: 'info',
        category: 'data_availability',
        issue: `Found ${constructionData.length} construction booking records`,
        description: 'Construction bookings are being stored in the database.',
        details: {
          service_types: [...new Set(constructionData.map(b => b.service_type))],
          date_range: {
            earliest: constructionData[constructionData.length - 1]?.created_at,
            latest: constructionData[0]?.created_at
          }
        }
      });
      
      // Check data completeness
      const incompleteRecords = constructionData.filter(record => 
        !record.property_details || 
        !record.service_details || 
        !record.contact
      );
      
      if (incompleteRecords.length > 0) {
        findings.push({
          severity: 'warning',
          category: 'data_quality',
          issue: `${incompleteRecords.length} construction bookings have incomplete data`,
          description: 'Some construction bookings are missing required JSONB field data.',
          impact: 'Forms may be submitting but not capturing all user input correctly.'
        });
      }
    }
  }
  
  // Analyze service type distribution
  if (queryResults.serviceTypeCounts?.success) {
    const counts = queryResults.serviceTypeCounts.data || [];
    const constructionTypes = counts.filter(c => 
      c.service_type.includes('construction') || c.service_type === 'construction'
    );
    
    if (constructionTypes.length > 0) {
      findings.push({
        severity: 'info',
        category: 'service_type_analysis',
        issue: 'Construction service types found',
        description: 'Multiple construction-related service types are being stored.',
        details: constructionTypes
      });
    }
  }
  
  return findings;
}

// Generate recommendations
function generateRecommendations(findings) {
  const recommendations = [];
  
  const criticalIssues = findings.filter(f => f.severity === 'critical');
  const dataQualityIssues = findings.filter(f => f.category === 'data_quality');
  
  if (criticalIssues.some(f => f.issue.includes('No construction bookings'))) {
    recommendations.push({
      priority: 'high',
      action: 'Investigate form submission process',
      description: 'No construction bookings found in database. Check form submission code, validation, and error handling.',
      technical_steps: [
        'Review construction form service type handling',
        'Check browser console for JavaScript errors during form submission',
        'Verify Supabase RLS policies allow construction booking insertion',
        'Test form submission with detailed logging enabled'
      ]
    });
  }
  
  if (dataQualityIssues.length > 0) {
    recommendations.push({
      priority: 'medium',
      action: 'Fix data completeness issues',
      description: 'Some construction bookings have incomplete JSONB field data.',
      technical_steps: [
        'Review form data serialization before database insertion',
        'Add validation to ensure all required fields are captured',
        'Implement data integrity checks in the submission process'
      ]
    });
  }
  
  recommendations.push({
    priority: 'low',
    action: 'Add monitoring and alerting',
    description: 'Implement proactive monitoring to detect booking submission failures.',
    technical_steps: [
      'Set up database monitoring for booking insertion failures',
      'Add application-level logging for form submissions',
      'Create alerts for missing or incomplete bookings'
    ]
  });
  
  return recommendations;
}

// Write investigation report
async function writeInvestigationReport(results) {
  const report = `# DATABASE CONSTRUCTION FORM INVESTIGATION REPORT

**Investigation Date:** ${results.timestamp}

## Executive Summary

- **Queries Executed:** ${results.investigation_summary.queries_executed || 0}
- **Successful Queries:** ${results.investigation_summary.successful_queries || 0}
- **Failed Queries:** ${results.investigation_summary.failed_queries || 0}
- **Total Findings:** ${results.investigation_summary.total_findings || 0}
- **Critical Issues:** ${results.investigation_summary.critical_issues || 0}

## Investigation Results

${Object.entries(results.query_results).map(([queryName, result]) => `
### Query: ${queryName}

${result.error ? `❌ **Error:** ${result.error}` : `✅ **Success:** ${result.row_count} rows returned`}

${result.success && result.data ? `
\`\`\`json
${JSON.stringify(result.data, null, 2)}
\`\`\`
` : ''}
`).join('\n')}

## Key Findings

${results.findings.map((finding, index) => `
### ${index + 1}. ${finding.issue} (${finding.severity.toUpperCase()})

**Category:** ${finding.category}  
**Description:** ${finding.description}

${finding.impact ? `**Impact:** ${finding.impact}` : ''}

${finding.details ? `
**Details:**
\`\`\`json
${JSON.stringify(finding.details, null, 2)}
\`\`\`
` : ''}
`).join('\n')}

## Recommendations

${results.recommendations.map((rec, index) => `
### ${index + 1}. ${rec.action} (${rec.priority.toUpperCase()} Priority)

**Description:** ${rec.description}

**Technical Steps:**
${rec.technical_steps.map(step => `- ${step}`).join('\n')}
`).join('\n')}

## Next Steps

1. **Immediate Actions:** Address any critical issues found in this investigation
2. **Data Validation:** Run additional queries to verify specific user reports
3. **Form Testing:** Test construction form submission process end-to-end
4. **Monitoring Setup:** Implement ongoing monitoring to prevent future issues

## SQL Queries Used

${Object.entries(INVESTIGATION_QUERIES).map(([name, query]) => `
### ${name}
\`\`\`sql
${query.trim()}
\`\`\`
`).join('\n')}

---
*This report was generated by the Database Construction Form Investigation Script*
*Data has been anonymized for security purposes*
`;
  
  fs.writeFileSync(INVESTIGATION_CONFIG.outputFile, report, 'utf8');
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
  runInvestigation()
    .then((results) => {
      console.log('\n📊 Investigation Summary:');
      console.log(`- Successful queries: ${results.investigation_summary.successful_queries || 0}`);
      console.log(`- Critical issues: ${results.investigation_summary.critical_issues || 0}`);
      console.log(`- Total findings: ${results.investigation_summary.total_findings || 0}`);
      
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Investigation failed:', error.message);
      process.exit(1);
    });
}

export { runInvestigation };