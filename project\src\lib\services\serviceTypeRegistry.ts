/**
 * Centralized Service Type Registry
 *
 * This module provides a single source of truth for all service types,
 * their mappings, and validation logic across the entire application.
 *
 * CRITICAL: This fixes major inconsistencies where different parts of the system
 * used conflicting service type values, causing database constraint violations
 * and booking failures.
 */

// ============================================================================
// Core Service Type Definitions
// ============================================================================

export interface ServiceType {
  id: string;
  name: string;
  category: 'residential' | 'commercial';
  description: string;
  basePrice?: number;
  isActive: boolean;
  aliases: string[]; // Alternative names/IDs for this service
  dbValue: string; // Exact value to use in database
  formValue?: string; // Value used in forms (if different from dbValue)
  displayName: string; // User-friendly display name
}

export const SERVICE_TYPES: Record<string, ServiceType> = {
  // ============================================================================
  // RESIDENTIAL SERVICES
  // ============================================================================

  residential_regular: {
    id: 'residential_regular',
    name: 'Regular House Cleaning',
    displayName: 'Regular House Cleaning',
    dbValue: 'residential_regular',
    category: 'residential',
    description: 'Standard residential cleaning service',
    basePrice: 120,
    isActive: true,
    aliases: [
      'regular', 'house', 'home', 'Regular House Cleaning',
      'residential-regular', 'house-cleaning', 'home-cleaning'
    ]
  },

  residential_deep: {
    id: 'residential_deep',
    name: 'Deep House Cleaning',
    displayName: 'Deep House Cleaning',
    dbValue: 'residential_deep',
    category: 'residential',
    description: 'Comprehensive deep cleaning service',
    basePrice: 180,
    isActive: true,
    aliases: [
      'deep', 'Deep House Cleaning', 'deep-clean', 'deep-cleaning',
      'residential-deep', 'deep-house', 'Deep Cleaning Service'
    ]
  },

  residential_move: {
    id: 'residential_move',
    name: 'Move-in/Move-out Cleaning',
    displayName: 'Move-in/Move-out Cleaning',
    dbValue: 'residential_move',
    category: 'residential',
    description: 'Specialized cleaning for moving',
    basePrice: 200,
    isActive: true,
    aliases: [
      'move', 'move-out', 'Move-in/Move-out Cleaning', 'moving',
      'move-in', 'moveout', 'movein', 'residential-move'
    ]
  },

  residential: {
    id: 'residential',
    name: 'Residential Cleaning',
    displayName: 'Residential Cleaning',
    dbValue: 'residential',
    category: 'residential',
    description: 'General residential cleaning service',
    basePrice: 120,
    isActive: true,
    aliases: ['home-cleaning', 'house-cleaning', 'residential-cleaning']
  },

  // ============================================================================
  // COMMERCIAL SERVICES
  // ============================================================================

  office: {
    id: 'office',
    name: 'Office Cleaning',
    displayName: 'Office Cleaning',
    dbValue: 'office',
    category: 'commercial',
    description: 'Professional office cleaning service',
    basePrice: 150,
    isActive: true,
    aliases: ['commercial', 'business', 'workplace', 'corporate']
  },
  
  carpet: {
    id: 'carpet',
    name: 'Carpet Cleaning',
    displayName: 'Carpet Cleaning',
    dbValue: 'carpet',
    category: 'commercial',
    description: 'Professional carpet cleaning service',
    basePrice: 89,
    isActive: true,
    aliases: ['rug', 'carpet-cleaning', 'floor-carpet', 'rugs']
  },
  
  window: {
    id: 'window',
    name: 'Window Cleaning',
    displayName: 'Window Cleaning',
    dbValue: 'window',
    category: 'commercial',
    description: 'Professional window cleaning service',
    basePrice: 75,
    isActive: true,
    aliases: ['windows', 'glass']
  },

  construction: {
    id: 'construction',
    name: 'Commercial Post-Construction Cleaning',
    displayName: 'Commercial Post-Construction Cleaning',
    dbValue: 'construction',
    category: 'commercial',
    description: 'Specialized commercial post-construction cleanup for offices, businesses, and industrial facilities',
    basePrice: 300,
    isActive: true,
    aliases: [
      'post-construction', 'construction-cleanup', 'renovation', 'commercial-construction',
      'business-post-construction', 'office-construction-cleanup', 'commercial-renovation',
      'post-construction-commercial'
    ]
  },

  sanitization: {
    id: 'sanitization',
    name: 'Sanitization & Disinfection',
    displayName: 'Sanitization & Disinfection',
    dbValue: 'sanitization',
    category: 'commercial',
    description: 'Professional sanitization service',
    basePrice: 200,
    isActive: true,
    aliases: ['disinfection', 'sanitize', 'covid-cleaning']
  },
  
  tile: {
    id: 'tile',
    name: 'Tile & Grout Cleaning',
    displayName: 'Tile & Grout Cleaning',
    dbValue: 'tile',
    category: 'commercial',
    description: 'Specialized tile and grout cleaning',
    basePrice: 150,
    isActive: true,
    aliases: ['grout', 'tile-cleaning', 'bathroom-tile']
  },

  pressure: {
    id: 'pressure',
    name: 'Pressure Washing',
    displayName: 'Pressure Washing',
    dbValue: 'pressure',
    category: 'commercial',
    description: 'High-pressure exterior cleaning',
    basePrice: 199,
    isActive: true,
    aliases: ['pressure-washing', 'power-washing', 'exterior']
  },

  floor: {
    id: 'floor',
    name: 'Floor Restoration',
    displayName: 'Floor Restoration',
    dbValue: 'floor',
    category: 'commercial',
    description: 'Professional floor restoration service',
    basePrice: 250,
    isActive: true,
    aliases: ['flooring', 'hardwood', 'floor-refinishing']
  },

  pool: {
    id: 'pool',
    name: 'Pool Cleaning',
    displayName: 'Pool Cleaning',
    dbValue: 'pool',
    category: 'residential',
    description: 'Professional pool cleaning service',
    basePrice: 125,
    isActive: true,
    aliases: ['swimming-pool', 'pool-maintenance', 'pool-cleaning']
  },
  
  chimney: {
    id: 'chimney',
    name: 'Chimney Cleaning',
    displayName: 'Chimney Cleaning',
    dbValue: 'chimney',
    category: 'commercial',
    description: 'Professional chimney cleaning service',
    basePrice: 175,
    isActive: true,
    aliases: ['fireplace', 'chimney-sweep', 'chimney-cleaning']
  },

  'waste-management': {
    id: 'waste-management',
    name: 'Waste Management',
    displayName: 'Waste Management',
    dbValue: 'waste-management',
    category: 'commercial',
    description: 'Professional waste management service',
    basePrice: 100,
    isActive: true,
    aliases: ['waste', 'garbage', 'trash']
  },

  industrial: {
    id: 'industrial',
    name: 'Industrial Cleaning',
    displayName: 'Industrial Cleaning',
    dbValue: 'industrial',
    category: 'commercial',
    description: 'Heavy-duty cleaning for industrial facilities and equipment',
    basePrice: 250,
    isActive: true,
    aliases: ['industrial-cleaning', 'factory-cleaning', 'warehouse-cleaning', 'equipment-cleaning', 'manufacturing-cleaning', 'industrial_cleaning']
  },

  'residential_post_construction': {
    id: 'residential_post_construction',
    name: 'Residential Post-Construction Cleaning',
    displayName: 'Residential Post-Construction Cleaning',
    dbValue: 'residential_post_construction',
    category: 'residential',
    description: 'Specialized post-construction cleanup for residential properties, homes, and apartments',
    basePrice: 250,
    isActive: true,
    aliases: [
      'post-construction-cleaning', 'construction-cleanup', 'renovation-cleaning',
      'post-construction', 'residential-construction', 'construction-clean',
      'home-post-construction', 'residential-renovation', 'house-construction-cleanup',
      'post-construction-residential'
    ]
  },

  residential_event: {
    id: 'residential_event',
    name: 'Residential Event Cleaning',
    displayName: 'Event Cleaning',
    dbValue: 'residential_event',
    category: 'residential',
    description: 'Pre and post-event cleaning for residential parties and gatherings',
    basePrice: 149,
    isActive: true,
    aliases: [
      'event-cleaning', 'event', 'party-cleaning', 'party',
      'celebration-cleaning', 'gathering-cleanup', 'event-prep',
      'pre-event', 'post-event', 'eventCleaning'
    ]
  },

  residential_upholstery: {
    id: 'residential_upholstery',
    name: 'Residential Upholstery Cleaning',
    displayName: 'Upholstery Cleaning',
    dbValue: 'residential_upholstery',
    category: 'residential',
    description: 'Professional upholstery and furniture cleaning',
    basePrice: 138,
    isActive: true,
    aliases: [
      'upholstery-cleaning', 'upholstery', 'furniture-cleaning',
      'couch-cleaning', 'sofa-cleaning', 'chair-cleaning',
      'fabric-cleaning', 'upholsteryService'
    ]
  }

  // ============================================================================
  // ADDITIONAL SERVICE TYPES (for future expansion)
  // ============================================================================

  // Note: Add new service types here following the same pattern
  // Remember to update database constraints when adding new types
};

// ============================================================================
// Service Type Utility Functions
// ============================================================================

/**
 * Normalize any service type input to a standardized service type ID
 */
export function normalizeServiceType(input: string): string {
  if (!input) return 'residential';

  const inputLower = input.toLowerCase().trim();
  console.log(`🔍 DEBUG: Normalizing "${input}" -> "${inputLower}"`);

  // FIRST: Check exact matches with input as-is (case sensitive)
  if (SERVICE_TYPES[input]) {
    console.log(`✅ DEBUG: Found exact match in SERVICE_TYPES for "${input}"`);
    return SERVICE_TYPES[input].dbValue;
  }

  // SECOND: Check exact matches with standard valid service types
  const validServiceTypes = [
    'residential_regular', 'residential_deep', 'residential_move', 'residential',
    'residential_post_construction', 'residential_event', 'residential_upholstery',
    'office', 'carpet', 'window', 'construction', 
    'sanitization', 'tile', 'pressure', 'floor', 'pool', 'chimney', 'waste-management', 'industrial'
  ];
  
  if (validServiceTypes.includes(input)) {
    console.log(`✅ DEBUG: Found "${input}" in validServiceTypes - returning as-is`);
    return input; // Return as-is if it's a valid service type
  }

  // THIRD: Search through aliases - exact match first
  for (const [serviceId, serviceType] of Object.entries(SERVICE_TYPES)) {
    if (serviceType.aliases.some(alias => alias.toLowerCase() === inputLower)) {
      const matchedAlias = serviceType.aliases.find(a => a.toLowerCase() === inputLower);
      console.log(`✅ DEBUG: Found exact alias match for "${input}" -> ${serviceType.dbValue} (matched alias: "${matchedAlias}")`);
      return serviceType.dbValue;
    }
  }

  // FOURTH: Then search for partial matches in aliases
  for (const [serviceId, serviceType] of Object.entries(SERVICE_TYPES)) {
    if (serviceType.aliases.some(alias => inputLower.includes(alias.toLowerCase()))) {
      const matchedAlias = serviceType.aliases.find(a => inputLower.includes(a.toLowerCase()));
      console.log(`⚠️ DEBUG: Found partial alias match for "${input}" -> ${serviceType.dbValue} (matched alias: "${matchedAlias}")`);
      return serviceType.dbValue;
    }
  }

  // FIFTH: Enhanced fallback logic for common patterns
  if (inputLower.includes('post') && inputLower.includes('construction')) {
    // Determine if it's commercial or residential based on context
    if (inputLower.includes('commercial') || 
        inputLower.includes('office') || 
        inputLower.includes('business') ||
        inputLower.includes('industrial') ||
        inputLower.includes('corporate')) {
      console.log(`✅ DEBUG: Matched commercial post-construction pattern for "${input}"`);
      return 'construction';  // Commercial post-construction
    } else {
      console.log(`✅ DEBUG: Matched residential post-construction pattern for "${input}"`);
      return 'residential_post_construction';  // Default to residential post-construction
    }
  }
  if (inputLower.includes('event') || inputLower.includes('party') || inputLower.includes('celebration')) {
    console.log(`✅ DEBUG: Matched event cleaning pattern for "${input}"`);
    return 'residential_event';
  }
  if (inputLower.includes('upholstery') || inputLower.includes('furniture') || inputLower.includes('couch') || inputLower.includes('sofa')) {
    console.log(`✅ DEBUG: Matched upholstery cleaning pattern for "${input}"`);
    return 'residential_upholstery';
  }
  if (inputLower.includes('chimney')) {
    console.log(`✅ DEBUG: Matched chimney pattern for "${input}"`);
    return 'chimney';
  }
  if (inputLower.includes('regular') || (inputLower.includes('house') && inputLower.includes('cleaning'))) {
    console.log(`✅ DEBUG: Matched regular cleaning pattern for "${input}"`);
    return 'residential_regular';
  }
  if (inputLower.includes('deep') && (inputLower.includes('house') || inputLower.includes('residential') || inputLower.includes('service'))) {
    console.log(`✅ DEBUG: Matched deep cleaning pattern for "${input}"`);
    return 'residential_deep';
  }
  if (inputLower.includes('move') && (inputLower.includes('in') || inputLower.includes('out'))) {
    console.log(`✅ DEBUG: Matched move cleaning pattern for "${input}"`);
    return 'residential_move';
  }
  if (inputLower.includes('industrial') || inputLower.includes('factory') || inputLower.includes('warehouse') || inputLower.includes('manufacturing')) {
    console.log(`✅ DEBUG: Matched industrial pattern for "${input}"`);
    return 'industrial';
  }
  if (inputLower.includes('office') || inputLower.includes('commercial')) {
    console.log(`✅ DEBUG: Matched office/commercial pattern for "${input}"`);
    return 'office';
  }
  if (inputLower.includes('carpet')) {
    console.log(`✅ DEBUG: Matched carpet pattern for "${input}"`);
    return 'carpet';
  }
  if (inputLower.includes('window')) {
    console.log(`✅ DEBUG: Matched window pattern for "${input}"`);
    return 'window';
  }
  if (inputLower.includes('pool')) {
    return 'pool';
  }
  if (inputLower.includes('residential') || inputLower.includes('home')) {
    console.log(`⚠️ DEBUG: Using residential fallback for "${input}"`);
    return 'residential';
  }

  // FINAL: Default fallback
  console.log(`⚠️ DEBUG: Using default fallback for "${input}"`);
  return 'residential';
}

/**
 * Validate if a service type is valid
 */
export function isValidServiceType(serviceType: string): boolean {
  return !!SERVICE_TYPES[serviceType];
}

/**
 * Get service type information
 */
export function getServiceType(serviceTypeId: string): ServiceType | null {
  return SERVICE_TYPES[serviceTypeId] || null;
}

/**
 * Get all active service types
 */
export function getActiveServiceTypes(): ServiceType[] {
  return Object.values(SERVICE_TYPES).filter(service => service.isActive);
}

/**
 * Get service types by category
 */
export function getServiceTypesByCategory(category: 'residential' | 'commercial'): ServiceType[] {
  return Object.values(SERVICE_TYPES).filter(
    service => service.category === category && service.isActive
  );
}

/**
 * Get display name for a service type
 */
export function getServiceDisplayName(serviceTypeId: string): string {
  const serviceType = SERVICE_TYPES[serviceTypeId];
  return serviceType?.displayName || serviceType?.name || 'Cleaning Service';
}

/**
 * Get database value for a service type
 */
export function getServiceDbValue(serviceTypeId: string): string {
  const serviceType = SERVICE_TYPES[serviceTypeId];
  return serviceType?.dbValue || serviceTypeId;
}

/**
 * Get base price for a service type
 */
export function getServiceBasePrice(serviceTypeId: string): number {
  const serviceType = SERVICE_TYPES[serviceTypeId];
  return serviceType?.basePrice || 120; // Default base price
}

/**
 * Get all valid service type IDs for database constraints
 */
export function getAllValidServiceTypeIds(): string[] {
  return Object.keys(SERVICE_TYPES);
}

/**
 * Convert legacy service type names to current format
 */
export function migrateLegacyServiceType(legacyType: string): string {
  const migrations: Record<string, string> = {
    'Regular House Cleaning': 'residential_regular',
    'Deep House Cleaning': 'residential_deep',
    'Move-in/Move-out Cleaning': 'residential_move',
    'Commercial Sanitization Service': 'sanitization',
    'regular': 'residential_regular',
    'deep': 'residential_deep',
    'move': 'residential_move'
  };

  return migrations[legacyType] || normalizeServiceType(legacyType);
}

// ============================================================================
// Type Guards and Validation
// ============================================================================

export function isResidentialService(serviceTypeId: string): boolean {
  const serviceType = SERVICE_TYPES[serviceTypeId];
  return serviceType?.category === 'residential';
}

export function isCommercialService(serviceTypeId: string): boolean {
  const serviceType = SERVICE_TYPES[serviceTypeId];
  return serviceType?.category === 'commercial';
}

// ============================================================================
// Export for database constraints
// ============================================================================

/**
 * SQL-ready list of all valid service types for database constraints
 */
export const SQL_SERVICE_TYPE_CONSTRAINT = getAllValidServiceTypeIds()
  .map(id => `'${id}'`)
  .join(', ');

/**
 * Get all valid service type database values for constraints
 */
export function getAllValidDbValues(): string[] {
  return Object.values(SERVICE_TYPES).map(service => service.dbValue);
}

/**
 * SQL-ready constraint for database migrations
 */
export function getSqlConstraint(): string {
  return getAllValidDbValues().map(value => `'${value}'`).join(', ');
}



/**
 * Configuration object for easy import in other modules
 */
export const ServiceTypeConfig = {
  types: SERVICE_TYPES,
  normalize: normalizeServiceType,
  validate: isValidServiceType,
  getDisplayName: getServiceDisplayName,
  getDbValue: getServiceDbValue,
  getBasePrice: getServiceBasePrice,
  isResidential: isResidentialService,
  isCommercial: isCommercialService,
  migrate: migrateLegacyServiceType,
  getAllDbValues: getAllValidDbValues,
  getSqlConstraint: getSqlConstraint
} as const;
