/*
  UNIFIED POST-<PERSON><PERSON><PERSON>UCTION SERVICE TYPES MIGRATION
  
  MISSION: Fix service type inconsistencies causing construction form failures
  
  CRITICAL FIXES:
  1. Ensure both 'construction' (commercial) and 'residential_post_construction' (residential) are supported
  2. Add enhanced normalization for post-construction service type variations
  3. Update validation functions to handle both commercial and residential post-construction
  4. Create data consistency checks and fixes
  
  BACKEND ARCHITECT SOLUTION - Phase 1: Schema Unification
*/

-- ============================================================================
-- ENHANCED SERVICE TYPE CONSTRAINTS
-- ============================================================================

-- Update booking_forms service type constraint with comprehensive list
ALTER TABLE booking_forms DROP CONSTRAINT IF EXISTS booking_forms_service_type_check;

ALTER TABLE booking_forms 
ADD CONSTRAINT booking_forms_service_type_check CHECK (
  service_type IN (
    -- Residential Services
    'residential_regular', 
    'residential_deep', 
    'residential_move', 
    'residential',
    'residential_post_construction',  -- ✅ Residential post-construction
    'residential_event',
    'residential_upholstery',
    
    -- Commercial Services
    'office', 
    'carpet', 
    'window', 
    'construction',                   -- ✅ Commercial post-construction
    'sanitization',
    'tile', 
    'pressure', 
    'floor', 
    'pool', 
    'chimney', 
    'waste-management',
    'industrial'
  )
);

-- Update payment_records service type constraint
ALTER TABLE payment_records DROP CONSTRAINT IF EXISTS payment_records_service_type_check;

ALTER TABLE payment_records 
ADD CONSTRAINT payment_records_service_type_check CHECK (
  service_type IN (
    -- Residential Services
    'residential_regular',
    'residential_deep', 
    'residential_move',
    'residential',
    'residential_post_construction',  -- ✅ Residential post-construction
    'residential_event',
    'residential_upholstery',
    
    -- Commercial Services
    'office',
    'carpet',
    'window', 
    'construction',                   -- ✅ Commercial post-construction
    'sanitization',
    'tile',
    'pressure',
    'floor',
    'pool',
    'chimney',
    'waste-management',
    'industrial'
  )
);

-- ============================================================================
-- ENHANCED NORMALIZATION FUNCTION
-- ============================================================================

-- Enhanced normalize_service_type function with better post-construction handling
CREATE OR REPLACE FUNCTION normalize_service_type(input_service_type text)
RETURNS text AS $$
BEGIN
  -- Handle null/empty input
  IF input_service_type IS NULL OR input_service_type = '' THEN
    RETURN 'residential';
  END IF;
  
  -- Convert to lowercase for comparison
  DECLARE
    lower_input text := LOWER(TRIM(input_service_type));
  BEGIN
  
    -- Direct matches for valid service types (case-insensitive exact matches)
    IF lower_input IN (
      'residential_regular', 'residential_deep', 'residential_move', 'residential',
      'residential_post_construction', 'residential_event', 'residential_upholstery',
      'office', 'carpet', 'window', 'construction', 'sanitization',
      'tile', 'pressure', 'floor', 'pool', 'chimney', 'waste-management', 'industrial'
    ) THEN
      RETURN lower_input;
    END IF;
    
    -- Enhanced post-construction pattern matching
    IF lower_input LIKE '%post%construction%' OR 
       lower_input LIKE '%construction%cleanup%' OR
       lower_input LIKE '%renovation%clean%' OR
       lower_input LIKE '%construction%clean%' THEN
      
      -- Determine if it's commercial or residential based on context
      IF lower_input LIKE '%commercial%' OR 
         lower_input LIKE '%office%' OR 
         lower_input LIKE '%business%' OR
         lower_input LIKE '%industrial%' THEN
        RETURN 'construction';  -- Commercial post-construction
      ELSE
        RETURN 'residential_post_construction';  -- Residential post-construction
      END IF;
    END IF;
    
    -- Specific service type mappings
    CASE lower_input
      -- Residential mappings
      WHEN 'regular house cleaning', 'regular', 'house', 'home-cleaning' THEN
        RETURN 'residential_regular';
      WHEN 'deep house cleaning', 'deep', 'deep-cleaning' THEN
        RETURN 'residential_deep';
      WHEN 'move-in/move-out cleaning', 'move', 'move-out', 'moveout' THEN
        RETURN 'residential_move';
      WHEN 'event cleaning', 'event', 'party-cleaning' THEN
        RETURN 'residential_event';
      WHEN 'upholstery cleaning', 'upholstery', 'furniture-cleaning' THEN
        RETURN 'residential_upholstery';
        
      -- Commercial mappings
      WHEN 'commercial', 'business', 'office', 'workplace' THEN
        RETURN 'office';
      WHEN 'commercial sanitization service', 'sanitization', 'disinfection' THEN
        RETURN 'sanitization';
      WHEN 'carpet cleaning', 'carpet', 'rug-cleaning' THEN
        RETURN 'carpet';
      WHEN 'window cleaning', 'windows', 'glass-cleaning' THEN
        RETURN 'window';
      WHEN 'tile cleaning', 'tile', 'grout-cleaning' THEN
        RETURN 'tile';
      WHEN 'pressure washing', 'pressure', 'power-washing' THEN
        RETURN 'pressure';
      WHEN 'floor restoration', 'floor', 'floor-refinishing' THEN
        RETURN 'floor';
      WHEN 'pool cleaning', 'pool', 'swimming-pool' THEN
        RETURN 'pool';
      WHEN 'chimney cleaning', 'chimney', 'fireplace' THEN
        RETURN 'chimney';
      WHEN 'waste management', 'waste', 'garbage' THEN
        RETURN 'waste-management';
      WHEN 'industrial cleaning', 'industrial', 'factory-cleaning' THEN
        RETURN 'industrial';
      ELSE
        -- Default fallback
        RETURN 'residential';
    END CASE;
  END;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- ENHANCED VALIDATION FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION is_valid_service_type(service_type text)
RETURNS boolean AS $$
BEGIN
  RETURN service_type IN (
    'residential_regular', 'residential_deep', 'residential_move', 'residential',
    'residential_post_construction', 'residential_event', 'residential_upholstery',
    'office', 'carpet', 'window', 'construction', 'sanitization',
    'tile', 'pressure', 'floor', 'pool', 'chimney', 'waste-management', 'industrial'
  );
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- SERVICE TYPE CATEGORY HELPER FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION get_service_type_category(service_type text)
RETURNS text AS $$
BEGIN
  CASE service_type
    WHEN 'residential_regular', 'residential_deep', 'residential_move', 'residential',
         'residential_post_construction', 'residential_event', 'residential_upholstery', 'pool' THEN
      RETURN 'residential';
    WHEN 'office', 'carpet', 'window', 'construction', 'sanitization',
         'tile', 'pressure', 'floor', 'chimney', 'waste-management', 'industrial' THEN
      RETURN 'commercial';
    ELSE
      RETURN 'unknown';
  END CASE;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- DATA CONSISTENCY FIXES
-- ============================================================================

-- Fix any existing records with inconsistent service types
UPDATE booking_forms 
SET service_type = normalize_service_type(service_type)
WHERE NOT is_valid_service_type(service_type);

UPDATE payment_records 
SET service_type = normalize_service_type(service_type)
WHERE NOT is_valid_service_type(service_type);

-- ============================================================================
-- COMPREHENSIVE COMMENTS AND DOCUMENTATION
-- ============================================================================

COMMENT ON CONSTRAINT booking_forms_service_type_check ON booking_forms 
IS 'Unified service types supporting both commercial (construction) and residential (residential_post_construction) post-construction services - Enhanced 2025-08-06';

COMMENT ON CONSTRAINT payment_records_service_type_check ON payment_records 
IS 'Unified service types supporting both commercial (construction) and residential (residential_post_construction) post-construction services - Enhanced 2025-08-06';

COMMENT ON FUNCTION normalize_service_type(text) 
IS 'Enhanced normalization function with improved post-construction service type handling for both commercial and residential contexts';

COMMENT ON FUNCTION is_valid_service_type(text) 
IS 'Comprehensive validation function supporting unified service type schema including both post-construction variants';

COMMENT ON FUNCTION get_service_type_category(text) 
IS 'Helper function to determine service category (residential/commercial) for proper business logic routing';

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify all service types are properly supported
DO $$
DECLARE
    test_types text[] := ARRAY[
        'construction', 'residential_post_construction', 'office', 'residential',
        'carpet', 'window', 'sanitization', 'industrial', 'pool', 'chimney'
    ];
    test_type text;
BEGIN
    FOREACH test_type IN ARRAY test_types
    LOOP
        IF NOT is_valid_service_type(test_type) THEN
            RAISE EXCEPTION 'Service type validation failed for: %', test_type;
        END IF;
        
        RAISE NOTICE 'Service type % is valid, category: %', 
            test_type, get_service_type_category(test_type);
    END LOOP;
    
    RAISE NOTICE 'All service types validated successfully!';
END
$$;