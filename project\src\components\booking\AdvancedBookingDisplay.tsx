/**
 * Advanced Booking Display Component
 * 
 * Provides comprehensive booking management interface with:
 * - Multiple view modes (list, calendar, card)
 * - Advanced filtering and search
 * - Status management
 * - Bulk operations
 */

import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Calendar, Clock, MapPin, Users, Building, Search, Filter,
  MoreVertical, Edit2, Trash2, Eye, CheckCircle, XCircle,
  AlertTriangle, DollarSign, Grid, List, CalendarDays,
  Download, Upload, RefreshCw, Settings
} from 'lucide-react';
import { StandardizedBookingData } from '../../lib/api/bookingService';
import { EnhancedBookingService, BookingSearchFilters } from '../../lib/services/enhancedBookingService';
import { useAuth } from '../../lib/auth/AuthProvider';

export interface AdvancedBookingDisplayProps {
  userId?: string;
  adminMode?: boolean;
  initialFilters?: BookingSearchFilters;
  onBookingSelect?: (booking: StandardizedBookingData) => void;
  onBookingUpdate?: (bookingId: string, updates: Partial<StandardizedBookingData>) => void;
}

type ViewMode = 'list' | 'grid' | 'calendar';
type SortOption = 'date' | 'status' | 'service' | 'price';

interface BookingStats {
  total: number;
  pending: number;
  confirmed: number;
  completed: number;
  cancelled: number;
  totalRevenue: number;
}

export function AdvancedBookingDisplay({
  userId,
  adminMode = false,
  initialFilters = {},
  onBookingSelect,
  onBookingUpdate
}: AdvancedBookingDisplayProps) {
  const { user } = useAuth();
  const [bookings, setBookings] = useState<StandardizedBookingData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // View and filter state
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<BookingSearchFilters>(initialFilters);
  const [sortBy, setSortBy] = useState<SortOption>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // Selection and bulk operations
  const [selectedBookings, setSelectedBookings] = useState<Set<string>>(new Set());
  const [showBulkActions, setShowBulkActions] = useState(false);
  
  // Statistics
  const [stats, setStats] = useState<BookingStats>({
    total: 0,
    pending: 0,
    confirmed: 0,
    completed: 0,
    cancelled: 0,
    totalRevenue: 0
  });

  // Load bookings
  const loadBookings = async () => {
    try {
      setLoading(true);
      setError(null);

      const searchFilters: BookingSearchFilters = {
        ...filters,
        customerEmail: searchTerm.includes('@') ? searchTerm : undefined,
        propertyAddress: !searchTerm.includes('@') && searchTerm ? searchTerm : undefined
      };

      const result = await EnhancedBookingService.searchBookings(
        searchFilters,
        {
          sortBy: sortBy === 'date' ? 'created_at' : sortBy === 'price' ? 'total_price' : sortBy,
          sortOrder,
          userId: adminMode ? undefined : userId || user?.id
        }
      );

      setBookings(result.bookings);
      
      // Calculate statistics
      const newStats = result.bookings.reduce((acc, booking) => {
        acc.total++;
        acc[booking.status as keyof BookingStats] = (acc[booking.status as keyof BookingStats] as number || 0) + 1;
        acc.totalRevenue += Number(booking.total_price) || 0;
        return acc;
      }, {
        total: 0,
        pending: 0,
        confirmed: 0,
        completed: 0,
        cancelled: 0,
        totalRevenue: 0
      });

      setStats(newStats);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load bookings');
      console.error('Failed to load bookings:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadBookings();
  }, [filters, searchTerm, sortBy, sortOrder, userId, user?.id, adminMode]);

  // Filter bookings based on search and filters
  const filteredBookings = useMemo(() => {
    return bookings.filter(booking => {
      if (searchTerm && !searchTerm.includes('@')) {
        const searchLower = searchTerm.toLowerCase();
        return (
          booking.contact?.firstName?.toLowerCase().includes(searchLower) ||
          booking.contact?.lastName?.toLowerCase().includes(searchLower) ||
          booking.contact?.email?.toLowerCase().includes(searchLower) ||
          booking.property_details?.address?.toLowerCase().includes(searchLower) ||
          booking.service_type?.toLowerCase().includes(searchLower)
        );
      }
      return true;
    });
  }, [bookings, searchTerm]);

  // Handle booking selection
  const handleBookingSelect = (bookingId: string, selected: boolean) => {
    const newSelection = new Set(selectedBookings);
    if (selected) {
      newSelection.add(bookingId);
    } else {
      newSelection.delete(bookingId);
    }
    setSelectedBookings(newSelection);
    setShowBulkActions(newSelection.size > 0);
  };

  // Handle bulk status update
  const handleBulkStatusUpdate = async (newStatus: StandardizedBookingData['status']) => {
    try {
      const result = await EnhancedBookingService.bulkUpdateStatus(
        Array.from(selectedBookings),
        newStatus,
        { userId: user?.id, reason: `Bulk update to ${newStatus}` }
      );

      if (result.successCount > 0) {
        await loadBookings();
        setSelectedBookings(new Set());
        setShowBulkActions(false);
      }

      if (result.failureCount > 0) {
        console.error('Some bookings failed to update:', result.errors);
      }

    } catch (error) {
      console.error('Bulk update failed:', error);
    }
  };

  // Handle individual booking update
  const handleBookingUpdate = async (
    bookingId: string, 
    updates: Partial<StandardizedBookingData>
  ) => {
    try {
      const result = await EnhancedBookingService.updateBooking(
        bookingId,
        updates,
        { userId: user?.id }
      );

      if (result.success) {
        await loadBookings();
        if (onBookingUpdate) {
          onBookingUpdate(bookingId, updates);
        }
      }
    } catch (error) {
      console.error('Failed to update booking:', error);
    }
  };

  // Render statistics bar
  const renderStatsBar = () => (
    <div className="bg-white/[0.08] backdrop-blur-[30px] border border-white/40 rounded-xl p-4 mb-6">
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-white">{stats.total}</div>
          <div className="text-sm text-gray-400">Total</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-yellow-400">{stats.pending}</div>
          <div className="text-sm text-gray-400">Pending</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-400">{stats.confirmed}</div>
          <div className="text-sm text-gray-400">Confirmed</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-400">{stats.completed}</div>
          <div className="text-sm text-gray-400">Completed</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-red-400">{stats.cancelled}</div>
          <div className="text-sm text-gray-400">Cancelled</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-emerald-400">${stats.totalRevenue.toFixed(0)}</div>
          <div className="text-sm text-gray-400">Revenue</div>
        </div>
      </div>
    </div>
  );

  // Render controls bar
  const renderControlsBar = () => (
    <div className="flex flex-col md:flex-row gap-4 mb-6">
      {/* Search */}
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <input
          type="text"
          placeholder="Search bookings..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-3 bg-white/[0.08] backdrop-blur-[30px] border border-white/40 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500/50"
        />
      </div>

      {/* View Mode Toggle */}
      <div className="flex bg-white/[0.08] backdrop-blur-[30px] border border-white/40 rounded-xl p-1">
        {[
          { mode: 'list' as ViewMode, icon: List },
          { mode: 'grid' as ViewMode, icon: Grid },
          { mode: 'calendar' as ViewMode, icon: CalendarDays }
        ].map(({ mode, icon: Icon }) => (
          <button
            key={mode}
            onClick={() => setViewMode(mode)}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === mode
                ? 'bg-emerald-500/20 text-emerald-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <Icon className="w-5 h-5" />
          </button>
        ))}
      </div>

      {/* Sort Options */}
      <select
        value={`${sortBy}-${sortOrder}`}
        onChange={(e) => {
          const [newSortBy, newSortOrder] = e.target.value.split('-');
          setSortBy(newSortBy as SortOption);
          setSortOrder(newSortOrder as 'asc' | 'desc');
        }}
        className="px-4 py-3 bg-white/[0.08] backdrop-blur-[30px] border border-white/40 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-500/50"
      >
        <option value="date-desc">Newest First</option>
        <option value="date-asc">Oldest First</option>
        <option value="status-asc">Status A-Z</option>
        <option value="service-asc">Service A-Z</option>
        <option value="price-desc">Price High-Low</option>
        <option value="price-asc">Price Low-High</option>
      </select>

      <button
        onClick={() => loadBookings()}
        className="px-4 py-3 bg-emerald-500/20 border border-emerald-500/40 rounded-xl text-emerald-400 hover:bg-emerald-500/30 transition-colors flex items-center gap-2"
      >
        <RefreshCw className="w-5 h-5" />
        Refresh
      </button>
    </div>
  );

  // Render bulk actions bar
  const renderBulkActionsBar = () => (
    <AnimatePresence>
      {showBulkActions && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="bg-emerald-500/20 border border-emerald-500/40 rounded-xl p-4 mb-4"
        >
          <div className="flex items-center justify-between">
            <span className="text-emerald-400 font-medium">
              {selectedBookings.size} booking(s) selected
            </span>
            <div className="flex gap-2">
              <button
                onClick={() => handleBulkStatusUpdate('confirmed')}
                className="px-3 py-1 bg-green-500/20 border border-green-500/40 rounded-lg text-green-400 hover:bg-green-500/30 transition-colors"
              >
                Confirm
              </button>
              <button
                onClick={() => handleBulkStatusUpdate('cancelled')}
                className="px-3 py-1 bg-red-500/20 border border-red-500/40 rounded-lg text-red-400 hover:bg-red-500/30 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  setSelectedBookings(new Set());
                  setShowBulkActions(false);
                }}
                className="px-3 py-1 bg-gray-500/20 border border-gray-500/40 rounded-lg text-gray-400 hover:bg-gray-500/30 transition-colors"
              >
                Clear
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  // Render booking card
  const renderBookingCard = (booking: StandardizedBookingData, index: number) => (
    <motion.div
      key={booking.id}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="bg-white/[0.08] backdrop-blur-[30px] border border-white/40 rounded-xl p-6 hover:bg-white/[0.12] transition-all duration-300"
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <input
            type="checkbox"
            checked={selectedBookings.has(booking.id!)}
            onChange={(e) => handleBookingSelect(booking.id!, e.target.checked)}
            className="w-4 h-4 text-emerald-500 rounded focus:ring-emerald-500"
          />
          <div>
            <h3 className="text-lg font-semibold text-white capitalize">
              {booking.service_type?.replace(/_/g, ' ')}
            </h3>
            <p className="text-gray-400 text-sm">
              {booking.contact?.firstName} {booking.contact?.lastName}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
            {booking.status}
          </span>
          <div className="relative">
            <button className="p-2 text-gray-400 hover:text-white transition-colors">
              <MoreVertical className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="flex items-center gap-2 text-gray-300">
          <MapPin className="w-4 h-4" />
          <span className="text-sm">{booking.property_details?.address}</span>
        </div>
        <div className="flex items-center gap-2 text-gray-300">
          <Calendar className="w-4 h-4" />
          <span className="text-sm">
            {booking.schedule?.preferredDate || 'Date TBD'}
          </span>
        </div>
        <div className="flex items-center gap-2 text-gray-300">
          <Clock className="w-4 h-4" />
          <span className="text-sm">
            {booking.schedule?.preferredTime || 'Time TBD'}
          </span>
        </div>
        <div className="flex items-center gap-2 text-gray-300">
          <DollarSign className="w-4 h-4" />
          <span className="text-sm font-medium">
            ${booking.total_price?.toFixed(2) || 'TBD'}
          </span>
        </div>
      </div>

      <div className="flex items-center justify-between pt-4 border-t border-white/20">
        <span className="text-xs text-gray-400">
          Created {new Date(booking.created_at!).toLocaleDateString()}
        </span>
        
        <div className="flex gap-2">
          <button
            onClick={() => onBookingSelect?.(booking)}
            className="p-2 text-emerald-400 hover:bg-emerald-500/20 rounded-lg transition-colors"
          >
            <Eye className="w-4 h-4" />
          </button>
          {adminMode && (
            <>
              <button
                onClick={() => handleBookingUpdate(booking.id!, { status: 'confirmed' })}
                className="p-2 text-green-400 hover:bg-green-500/20 rounded-lg transition-colors"
              >
                <CheckCircle className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleBookingUpdate(booking.id!, { status: 'cancelled' })}
                className="p-2 text-red-400 hover:bg-red-500/20 rounded-lg transition-colors"
              >
                <XCircle className="w-4 h-4" />
              </button>
            </>
          )}
        </div>
      </div>
    </motion.div>
  );

  // Get status color
  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/40',
      confirmed: 'bg-green-500/20 text-green-400 border border-green-500/40',
      in_progress: 'bg-blue-500/20 text-blue-400 border border-blue-500/40',
      completed: 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/40',
      cancelled: 'bg-red-500/20 text-red-400 border border-red-500/40',
      rescheduled: 'bg-purple-500/20 text-purple-400 border border-purple-500/40'
    };
    return colors[status as keyof typeof colors] || colors.pending;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-white text-lg">Loading bookings...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-500/20 border border-red-500/40 rounded-xl p-6 text-center">
        <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-red-400 mb-2">Error Loading Bookings</h3>
        <p className="text-red-300 mb-4">{error}</p>
        <button
          onClick={() => loadBookings()}
          className="px-4 py-2 bg-red-500/20 border border-red-500/40 rounded-lg text-red-400 hover:bg-red-500/30 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {renderStatsBar()}
      {renderControlsBar()}
      {renderBulkActionsBar()}

      {/* Bookings Display */}
      {filteredBookings.length === 0 ? (
        <div className="text-center py-12">
          <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">No bookings found</h3>
          <p className="text-gray-400">
            {searchTerm || Object.keys(filters).length > 0
              ? 'Try adjusting your search or filters'
              : 'No bookings have been created yet'}
          </p>
        </div>
      ) : (
        <div className={
          viewMode === 'grid'
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
        }>
          {filteredBookings.map((booking, index) => renderBookingCard(booking, index))}
        </div>
      )}
    </div>
  );
}
