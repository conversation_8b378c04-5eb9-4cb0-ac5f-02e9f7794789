#!/usr/bin/env node

/**
 * SERVICE TYPE DATA MIGRATION SCRIPT
 * 
 * MISSION: Fix existing data inconsistencies in service types
 * 
 * This script:
 * 1. Analyzes existing booking_forms and payment_records
 * 2. Identifies service type inconsistencies
 * 3. Migrates data using the enhanced normalization functions
 * 4. Generates a migration report
 * 
 * BACKEND ARCHITECT SOLUTION - Phase 2: Data Migration
 */

import { createClient } from '@supabase/supabase-js';
import { ServiceTypeStandardizer } from '../src/lib/services/serviceTypeStandardizer.js';

// Configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('❌ Missing Supabase configuration. Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

/**
 * Analyze service type distribution in a table
 */
async function analyzeServiceTypes(tableName) {
  console.log(`\n📊 Analyzing service types in ${tableName}...`);
  
  const { data, error } = await supabase
    .from(tableName)
    .select('id, service_type')
    .not('service_type', 'is', null);
    
  if (error) {
    console.error(`❌ Error fetching data from ${tableName}:`, error);
    return { total: 0, distribution: {}, issues: [] };
  }
  
  const distribution = {};
  const issues = [];
  let total = data.length;
  
  data.forEach(record => {
    const serviceType = record.service_type;
    distribution[serviceType] = (distribution[serviceType] || 0) + 1;
    
    // Check if service type is valid
    if (!ServiceTypeStandardizer.isValid(serviceType)) {
      issues.push({
        id: record.id,
        currentType: serviceType,
        suggestedType: ServiceTypeStandardizer.normalize(serviceType)
      });
    }
  });
  
  return { total, distribution, issues };
}

/**
 * Migrate service types in a table
 */
async function migrateServiceTypes(tableName, issues) {
  if (issues.length === 0) {
    console.log(`✅ No migration needed for ${tableName}`);
    return { success: 0, failed: 0 };
  }
  
  console.log(`\n🔧 Migrating ${issues.length} records in ${tableName}...`);
  
  let success = 0;
  let failed = 0;
  
  for (const issue of issues) {
    try {
      const { error } = await supabase
        .from(tableName)
        .update({ 
          service_type: issue.suggestedType,
          metadata: {
            ...{}, // Preserve existing metadata
            migration: {
              migratedAt: new Date().toISOString(),
              originalServiceType: issue.currentType,
              migratedServiceType: issue.suggestedType,
              migrationScript: 'migrate-service-types.js'
            }
          }
        })
        .eq('id', issue.id);
        
      if (error) {
        console.error(`❌ Failed to migrate record ${issue.id}:`, error);
        failed++;
      } else {
        console.log(`✅ Migrated ${issue.id}: "${issue.currentType}" -> "${issue.suggestedType}"`);
        success++;
      }
    } catch (err) {
      console.error(`❌ Unexpected error migrating record ${issue.id}:`, err);
      failed++;
    }
  }
  
  return { success, failed };
}

/**
 * Generate migration report
 */
function generateReport(results) {
  console.log(`\n📋 SERVICE TYPE MIGRATION REPORT`);
  console.log(`=====================================`);
  console.log(`Generated: ${new Date().toISOString()}\n`);
  
  let totalProcessed = 0;
  let totalMigrated = 0;
  let totalFailed = 0;
  
  for (const [tableName, result] of Object.entries(results)) {
    console.log(`\n📊 ${tableName.toUpperCase()}`);
    console.log(`   Total Records: ${result.analysis.total}`);
    console.log(`   Issues Found: ${result.analysis.issues.length}`);
    console.log(`   Successfully Migrated: ${result.migration.success}`);
    console.log(`   Failed Migrations: ${result.migration.failed}`);
    
    console.log(`   Service Type Distribution:`);
    for (const [type, count] of Object.entries(result.analysis.distribution)) {
      const isValid = ServiceTypeStandardizer.isValid(type);
      const status = isValid ? '✅' : '❌';
      console.log(`     ${status} ${type}: ${count} records`);
    }
    
    totalProcessed += result.analysis.total;
    totalMigrated += result.migration.success;
    totalFailed += result.migration.failed;
  }
  
  console.log(`\n📈 SUMMARY`);
  console.log(`   Total Records Processed: ${totalProcessed}`);
  console.log(`   Total Records Migrated: ${totalMigrated}`);
  console.log(`   Total Failed Migrations: ${totalFailed}`);
  console.log(`   Migration Success Rate: ${totalProcessed > 0 ? ((totalMigrated / (totalMigrated + totalFailed)) * 100).toFixed(2) : 0}%`);
  
  if (totalFailed > 0) {
    console.log(`\n⚠️  Some migrations failed. Please review the error messages above.`);
  } else if (totalMigrated > 0) {
    console.log(`\n🎉 All migrations completed successfully!`);
  } else {
    console.log(`\n✨ No migrations were needed. All service types are already consistent.`);
  }
}

/**
 * Main migration function
 */
async function runMigration() {
  console.log('🚀 Starting Service Type Migration...');
  console.log('=====================================\n');
  
  const tables = ['booking_forms', 'payment_records'];
  const results = {};
  
  // Phase 1: Analysis
  console.log('📊 PHASE 1: ANALYSIS');
  for (const table of tables) {
    results[table] = {
      analysis: await analyzeServiceTypes(table),
      migration: { success: 0, failed: 0 }
    };
  }
  
  // Phase 2: Migration
  console.log('\n🔧 PHASE 2: MIGRATION');
  for (const table of tables) {
    results[table].migration = await migrateServiceTypes(table, results[table].analysis.issues);
  }
  
  // Phase 3: Report
  console.log('\n📋 PHASE 3: REPORTING');
  generateReport(results);
  
  console.log('\n✅ Migration completed!');
}

/**
 * Dry run mode for testing
 */
async function runDryRun() {
  console.log('🔍 RUNNING DRY RUN (Analysis Only)...');
  console.log('====================================\n');
  
  const tables = ['booking_forms', 'payment_records'];
  const results = {};
  
  for (const table of tables) {
    results[table] = {
      analysis: await analyzeServiceTypes(table),
      migration: { success: 0, failed: 0 } // No actual migration in dry run
    };
  }
  
  // Show what would be migrated
  console.log('\n🔮 MIGRATION PREVIEW');
  for (const [tableName, result] of Object.entries(results)) {
    if (result.analysis.issues.length > 0) {
      console.log(`\n${tableName} - Would migrate ${result.analysis.issues.length} records:`);
      result.analysis.issues.forEach(issue => {
        console.log(`  📝 ID ${issue.id}: "${issue.currentType}" -> "${issue.suggestedType}"`);
      });
    }
  }
  
  generateReport(results);
}

// CLI Interface
const isDryRun = process.argv.includes('--dry-run') || process.argv.includes('-d');

if (isDryRun) {
  runDryRun().catch(console.error);
} else {
  const isForced = process.argv.includes('--force') || process.argv.includes('-f');
  
  if (!isForced) {
    console.log('⚠️  This script will modify your database.');
    console.log('   Use --dry-run to preview changes first.');
    console.log('   Use --force to proceed with actual migration.');
    process.exit(1);
  }
  
  runMigration().catch(console.error);
}