/**
 * Post-Construction Service Type Integration Tests
 * 
 * Tests the integration between post-construction forms and the unified backend infrastructure
 * to ensure correct service type handling for both commercial and residential contexts.
 */

import { ServiceTypeStandardizer } from '../lib/services/serviceTypeStandardizer';
import { normalizeServiceType, isValidServiceType } from '../lib/services/serviceTypeRegistry';

describe('Post-Construction Service Type Integration', () => {
  describe('Commercial Post-Construction Service Type', () => {
    it('should correctly standardize commercial post-construction service type', () => {
      const serviceType = ServiceTypeStandardizer.prepareServiceTypeForDb('construction');
      expect(serviceType).toBe('construction');
    });

    it('should validate commercial post-construction service type', () => {
      const serviceType = 'construction';
      expect(ServiceTypeStandardizer.isValid(serviceType)).toBe(true);
      expect(isValidServiceType(serviceType)).toBe(true);
    });

    it('should properly normalize various commercial post-construction inputs', () => {
      const inputs = [
        'commercial-construction',
        'post-construction-commercial',
        'business-post-construction',
        'office-construction-cleanup',
        'commercial-renovation'
      ];

      inputs.forEach(input => {
        const normalized = normalizeServiceType(input);
        expect(normalized).toBe('construction');
      });
    });

    it('should create standardized booking data for commercial forms', () => {
      const mockBookingData = {
        user_id: 'test-user',
        service_type: 'construction',
        contact: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '555-1234'
        }
      };

      const standardized = ServiceTypeStandardizer.standardizeBookingData(mockBookingData);
      
      expect(standardized.service_type).toBe('construction');
      expect(standardized.metadata).toBeDefined();
      expect(standardized.service_details.standardizedServiceType).toBe('construction');
      expect(standardized.service_details.displayName).toContain('Commercial');
    });
  });

  describe('Residential Post-Construction Service Type', () => {
    it('should correctly standardize residential post-construction service type', () => {
      const serviceType = ServiceTypeStandardizer.prepareServiceTypeForDb('residential_post_construction');
      expect(serviceType).toBe('residential_post_construction');
    });

    it('should validate residential post-construction service type', () => {
      const serviceType = 'residential_post_construction';
      expect(ServiceTypeStandardizer.isValid(serviceType)).toBe(true);
      expect(isValidServiceType(serviceType)).toBe(true);
    });

    it('should properly normalize various residential post-construction inputs', () => {
      const inputs = [
        'post-construction-cleaning',
        'construction-cleanup',
        'residential-construction',
        'home-post-construction',
        'house-construction-cleanup'
      ];

      inputs.forEach(input => {
        const normalized = normalizeServiceType(input);
        expect(normalized).toBe('residential_post_construction');
      });
    });

    it('should create standardized booking data for residential forms', () => {
      const mockBookingData = {
        user_id: 'test-user',
        service_type: 'residential_post_construction',
        contact: {
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          phone: '555-5678'
        }
      };

      const standardized = ServiceTypeStandardizer.standardizeBookingData(mockBookingData);
      
      expect(standardized.service_type).toBe('residential_post_construction');
      expect(standardized.metadata).toBeDefined();
      expect(standardized.service_details.standardizedServiceType).toBe('residential_post_construction');
      expect(standardized.service_details.displayName).toContain('Residential');
    });
  });

  describe('Service Type Differentiation', () => {
    it('should differentiate between commercial and residential post-construction', () => {
      const commercialInputs = [
        'commercial-construction',
        'office-post-construction',
        'business-renovation',
        'industrial-post-construction'
      ];

      const residentialInputs = [
        'home-post-construction',
        'residential-renovation',
        'house-construction-cleanup',
        'apartment-post-construction'
      ];

      commercialInputs.forEach(input => {
        const normalized = normalizeServiceType(input);
        expect(normalized).toBe('construction');
      });

      residentialInputs.forEach(input => {
        const normalized = normalizeServiceType(input);
        expect(normalized).toBe('residential_post_construction');
      });
    });

    it('should handle ambiguous inputs correctly', () => {
      // When context is unclear, should default to residential for safety
      const ambiguousInputs = [
        'post-construction',
        'construction-cleanup',
        'renovation-cleaning'
      ];

      ambiguousInputs.forEach(input => {
        const normalized = normalizeServiceType(input);
        expect(normalized).toBe('residential_post_construction');
      });
    });
  });

  describe('Form Data Standardization', () => {
    it('should standardize commercial form data correctly', () => {
      const mockFormData = {
        serviceType: 'construction',
        firstName: 'John',
        lastName: 'Doe',
        companyName: 'Test Company',
        projectType: 'office-renovation',
        servicePackage: 'final-clean'
      };

      const standardized = ServiceTypeStandardizer.standardizeFormServiceType(mockFormData);
      
      expect(standardized.serviceType).toBe('construction');
      expect(standardized.metadata.originalServiceType).toBe('construction');
      expect(standardized.metadata.standardizedServiceType).toBe('construction');
    });

    it('should standardize residential form data correctly', () => {
      const mockFormData = {
        serviceType: 'residential_post_construction',
        firstName: 'Jane',
        lastName: 'Smith',
        propertyType: 'house',
        constructionType: 'renovation'
      };

      const standardized = ServiceTypeStandardizer.standardizeFormServiceType(mockFormData);
      
      expect(standardized.serviceType).toBe('residential_post_construction');
      expect(standardized.metadata.originalServiceType).toBe('residential_post_construction');
      expect(standardized.metadata.standardizedServiceType).toBe('residential_post_construction');
    });
  });

  describe('Error Handling and Validation', () => {
    it('should handle invalid service types gracefully', () => {
      const invalidServiceType = 'invalid-service-type';
      
      // Should not throw error, but return a fallback
      const prepared = ServiceTypeStandardizer.prepareServiceTypeForDb(invalidServiceType);
      expect(prepared).toBeDefined();
      expect(typeof prepared).toBe('string');
    });

    it('should validate service types correctly', () => {
      const validTypes = ['construction', 'residential_post_construction'];
      const invalidTypes = ['invalid-type', '', null, undefined];

      validTypes.forEach(type => {
        expect(ServiceTypeStandardizer.isValid(type)).toBe(true);
      });

      invalidTypes.forEach(type => {
        expect(ServiceTypeStandardizer.isValid(type)).toBe(false);
      });
    });
  });

  describe('Payment Integration', () => {
    it('should standardize payment data for commercial services', () => {
      const paymentData = {
        service_type: 'construction',
        amount: 500,
        customerEmail: '<EMAIL>',
        formData: {
          serviceType: 'construction',
          companyName: 'Test Corp'
        }
      };

      const standardized = ServiceTypeStandardizer.standardizePaymentServiceType(paymentData);
      
      expect(standardized.service_type).toBe('construction');
      expect(standardized.formData.serviceType).toBe('construction');
    });

    it('should standardize payment data for residential services', () => {
      const paymentData = {
        service_type: 'residential_post_construction',
        amount: 300,
        customerEmail: '<EMAIL>',
        formData: {
          serviceType: 'residential_post_construction',
          propertyType: 'house'
        }
      };

      const standardized = ServiceTypeStandardizer.standardizePaymentServiceType(paymentData);
      
      expect(standardized.service_type).toBe('residential_post_construction');
      expect(standardized.formData.serviceType).toBe('residential_post_construction');
    });
  });

  describe('Database Integration', () => {
    it('should prepare correct database values for commercial services', () => {
      const dbValue = ServiceTypeStandardizer.getDbValue('construction');
      expect(dbValue).toBe('construction');
    });

    it('should prepare correct database values for residential services', () => {
      const dbValue = ServiceTypeStandardizer.getDbValue('residential_post_construction');
      expect(dbValue).toBe('residential_post_construction');
    });

    it('should create proper display names for UI', () => {
      const commercialDisplay = ServiceTypeStandardizer.getDisplayName('construction');
      const residentialDisplay = ServiceTypeStandardizer.getDisplayName('residential_post_construction');
      
      expect(commercialDisplay).toContain('Commercial');
      expect(residentialDisplay).toContain('Residential');
    });
  });
});