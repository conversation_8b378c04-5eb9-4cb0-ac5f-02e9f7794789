#!/usr/bin/env node

/**
 * STATIC CONSTRUCTION FORM ANALYSIS SCRIPT
 * 
 * This script performs static analysis of the codebase to understand
 * construction form data flow and potential issues without requiring
 * database connectivity.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Configuration
const ANALYSIS_CONFIG = {
  outputFile: path.join(__dirname, '../STATIC_CONSTRUCTION_ANALYSIS_RESULTS.md'),
  projectRoot: path.join(__dirname, '..')
};

// File patterns to search
const SEARCH_PATTERNS = {
  constructionForms: [
    '**/Construction*Form.tsx',
    '**/PostConstruction*Form.tsx',
    '**/*construction*.tsx'
  ],
  bookingSubmission: [
    '**/bookingForms.ts',
    '**/bookingService.ts',
    '**/submitBookingForm*'
  ],
  serviceTypes: [
    '**/serviceType*.ts',
    '**/serviceType*.tsx'
  ],
  migrations: [
    'supabase/migrations/*.sql'
  ]
};

// Results storage
const analysisResults = {
  timestamp: new Date().toISOString(),
  construction_forms: [],
  service_type_usage: [],
  database_constraints: [],
  potential_issues: [],
  recommendations: []
};

/**
 * Recursively find files matching a pattern
 */
function findFiles(dir, pattern) {
  const files = [];
  
  try {
    const items = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const item of items) {
      const fullPath = path.join(dir, item.name);
      
      // Skip node_modules and other ignored directories
      if (item.isDirectory() && !['node_modules', '.git', 'dist', 'build'].includes(item.name)) {
        files.push(...findFiles(fullPath, pattern));
      } else if (item.isFile() && matchesPattern(item.name, pattern)) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    // Ignore permission errors
  }
  
  return files;
}

/**
 * Check if filename matches pattern
 */
function matchesPattern(filename, pattern) {
  // Simple glob-like matching
  const regex = pattern
    .replace(/\*\*/g, '.*')
    .replace(/\*/g, '[^/]*')
    .replace(/\?/g, '[^/]');
  
  return new RegExp(regex, 'i').test(filename);
}

/**
 * Search for patterns in file content
 */
function searchInFile(filePath, patterns) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const results = [];
    
    for (const pattern of patterns) {
      const regex = new RegExp(pattern, 'gi');
      let match;
      
      while ((match = regex.exec(content)) !== null) {
        const lineNumber = content.substring(0, match.index).split('\n').length;
        results.push({
          pattern,
          match: match[0],
          lineNumber,
          context: getLineContext(content, lineNumber)
        });
      }
    }
    
    return results;
  } catch (error) {
    return [];
  }
}

/**
 * Get context around a line number
 */
function getLineContext(content, lineNumber) {
  const lines = content.split('\n');
  const start = Math.max(0, lineNumber - 3);
  const end = Math.min(lines.length, lineNumber + 2);
  
  return lines.slice(start, end).join('\n');
}

/**
 * Analyze construction forms
 */
function analyzeConstructionForms() {
  console.log('🔍 Analyzing construction forms...');
  
  const formFiles = [];
  
  // Find all construction-related form files
  for (const pattern of SEARCH_PATTERNS.constructionForms) {
    const files = findFiles(ANALYSIS_CONFIG.projectRoot, pattern);
    formFiles.push(...files);
  }
  
  // Remove duplicates
  const uniqueFormFiles = [...new Set(formFiles)];
  
  console.log(`📄 Found ${uniqueFormFiles.length} construction form files`);
  
  for (const file of uniqueFormFiles) {
    const relativePath = path.relative(ANALYSIS_CONFIG.projectRoot, file);
    
    // Search for service type definitions
    const serviceTypePatterns = [
      'service[Tt]ype.*=.*[\'"`]([^\'"`]+)[\'"`]',
      'ServiceTypeStandardizer\\.prepareServiceTypeForDb\\([\'"`]([^\'"`]+)[\'"`]\\)',
      'submitBookingForm\\([^,]*,\\s*[\'"`]([^\'"`]+)[\'"`]'
    ];
    
    const matches = searchInFile(file, serviceTypePatterns);
    
    analysisResults.construction_forms.push({
      file: relativePath,
      service_type_matches: matches,
      issues: analyzeFormIssues(file, matches)
    });
  }
}

/**
 * Analyze form issues
 */
function analyzeFormIssues(filePath, matches) {
  const issues = [];
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Check for common issues
  if (content.includes('residential_post_construction') && content.includes('construction')) {
    issues.push('Form may have conflicting service types');
  }
  
  if (!content.includes('ServiceTypeStandardizer')) {
    issues.push('Form does not use ServiceTypeStandardizer');
  }
  
  if (!content.includes('submitBookingForm')) {
    issues.push('Form does not appear to submit data');
  }
  
  return issues;
}

/**
 * Analyze database constraints from migrations
 */
function analyzeDatabaseConstraints() {
  console.log('🗄️ Analyzing database constraints...');
  
  const migrationDir = path.join(ANALYSIS_CONFIG.projectRoot, 'supabase/migrations');
  
  if (!fs.existsSync(migrationDir)) {
    console.log('⚠️ Migration directory not found');
    return;
  }
  
  const migrationFiles = fs.readdirSync(migrationDir)
    .filter(file => file.endsWith('.sql'))
    .sort();
  
  for (const file of migrationFiles) {
    const filePath = path.join(migrationDir, file);
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Look for service type constraints
    const constraintPattern = /CHECK\s*\(\s*service_type\s+IN\s*\(([\s\S]*?)\)\s*\)/gi;
    const matches = [...content.matchAll(constraintPattern)];
    
    if (matches.length > 0) {
      for (const match of matches) {
        const serviceTypes = extractServiceTypes(match[1]);
        
        analysisResults.database_constraints.push({
          migration: file,
          service_types: serviceTypes,
          has_construction: serviceTypes.includes('construction'),
          has_residential_post_construction: serviceTypes.includes('residential_post_construction')
        });
      }
    }
  }
}

/**
 * Extract service types from constraint definition
 */
function extractServiceTypes(constraintText) {
  const serviceTypes = [];
  const matches = constraintText.match(/'([^']+)'/g) || [];
  
  for (const match of matches) {
    const serviceType = match.replace(/'/g, '');
    if (serviceType.trim()) {
      serviceTypes.push(serviceType.trim());
    }
  }
  
  return serviceTypes;
}

/**
 * Analyze service type usage patterns
 */
function analyzeServiceTypeUsage() {
  console.log('🏷️ Analyzing service type usage...');
  
  const usageFiles = [];
  
  // Find service type related files
  for (const pattern of SEARCH_PATTERNS.serviceTypes) {
    const files = findFiles(ANALYSIS_CONFIG.projectRoot, pattern);
    usageFiles.push(...files);
  }
  
  // Also check booking submission files
  for (const pattern of SEARCH_PATTERNS.bookingSubmission) {
    const files = findFiles(ANALYSIS_CONFIG.projectRoot, pattern);
    usageFiles.push(...files);
  }
  
  const uniqueFiles = [...new Set(usageFiles)];
  
  for (const file of uniqueFiles) {
    const relativePath = path.relative(ANALYSIS_CONFIG.projectRoot, file);
    const content = fs.readFileSync(file, 'utf8');
    
    // Extract service type references
    const serviceTypeRefs = extractServiceTypeReferences(content);
    
    if (serviceTypeRefs.length > 0) {
      analysisResults.service_type_usage.push({
        file: relativePath,
        service_types: serviceTypeRefs
      });
    }
  }
}

/**
 * Extract service type references from content
 */
function extractServiceTypeReferences(content) {
  const refs = [];
  
  // Common patterns for service type references
  const patterns = [
    /'(.*construction.*?)'/gi,
    /"(.*construction.*?)"/gi,
    /case\s*['"`](.*construction.*?)['"`]/gi,
    /service_type.*['"`](.*construction.*?)['"`]/gi
  ];
  
  for (const pattern of patterns) {
    const matches = [...content.matchAll(pattern)];
    for (const match of matches) {
      if (match[1] && match[1].includes('construction')) {
        refs.push(match[1]);
      }
    }
  }
  
  return [...new Set(refs)];
}

/**
 * Identify potential issues
 */
function identifyPotentialIssues() {
  console.log('⚠️ Identifying potential issues...');
  
  // Check for service type inconsistencies
  const allServiceTypes = new Set();
  
  // Collect all service types from forms
  for (const form of analysisResults.construction_forms) {
    for (const match of form.service_type_matches) {
      if (match.match) {
        allServiceTypes.add(match.match);
      }
    }
  }
  
  // Collect all service types from usage
  for (const usage of analysisResults.service_type_usage) {
    for (const type of usage.service_types) {
      allServiceTypes.add(type);
    }
  }
  
  // Get latest database constraints
  const latestConstraints = analysisResults.database_constraints.length > 0
    ? analysisResults.database_constraints[analysisResults.database_constraints.length - 1]
    : null;
  
  if (latestConstraints) {
    const constrainedTypes = new Set(latestConstraints.service_types);
    
    // Check for service types used in code but not in constraints
    for (const type of allServiceTypes) {
      if (!constrainedTypes.has(type)) {
        analysisResults.potential_issues.push({
          severity: 'critical',
          issue: `Service type '${type}' used in code but not allowed in database constraint`,
          description: 'This would cause form submissions to fail with constraint violation errors'
        });
      }
    }
    
    // Check for specific construction issues
    const hasConstruction = constrainedTypes.has('construction');
    const hasResidentialPostConstruction = constrainedTypes.has('residential_post_construction');
    
    if (!hasConstruction && !hasResidentialPostConstruction) {
      analysisResults.potential_issues.push({
        severity: 'critical',
        issue: 'No construction service types found in database constraints',
        description: 'Construction forms will fail to submit due to constraint violations'
      });
    }
    
    if (hasConstruction && hasResidentialPostConstruction) {
      // Check if forms are using the correct service types
      const constructionFormsCount = analysisResults.construction_forms.length;
      if (constructionFormsCount === 0) {
        analysisResults.potential_issues.push({
          severity: 'warning',
          issue: 'Database supports construction service types but no forms found',
          description: 'Forms may not be properly connected or named'
        });
      }
    }
  }
}

/**
 * Generate recommendations
 */
function generateRecommendations() {
  console.log('💡 Generating recommendations...');
  
  const criticalIssues = analysisResults.potential_issues.filter(i => i.severity === 'critical');
  
  if (criticalIssues.length > 0) {
    analysisResults.recommendations.push({
      priority: 'high',
      action: 'Fix database constraint violations',
      description: 'Ensure all service types used in forms are allowed by database constraints',
      steps: [
        'Review the latest migration file for service type constraints',
        'Compare with service types used in construction forms',
        'Run a new migration to add missing service types if needed'
      ]
    });
  }
  
  const formIssues = analysisResults.construction_forms.filter(f => f.issues.length > 0);
  
  if (formIssues.length > 0) {
    analysisResults.recommendations.push({
      priority: 'medium',
      action: 'Standardize form implementations',
      description: 'Ensure all construction forms use consistent service type handling',
      steps: [
        'Update forms to use ServiceTypeStandardizer',
        'Ensure proper service type values are used',
        'Add proper form submission handling'
      ]
    });
  }
  
  analysisResults.recommendations.push({
    priority: 'low',
    action: 'Add comprehensive testing',
    description: 'Implement end-to-end testing for construction form submissions',
    steps: [
      'Create automated tests for each construction form type',
      'Test database insertion with various service types',
      'Monitor form submission success rates'
    ]
  });
}

/**
 * Write analysis report
 */
async function writeAnalysisReport() {
  const report = `# STATIC CONSTRUCTION FORM ANALYSIS REPORT

**Analysis Date:** ${analysisResults.timestamp}

## Executive Summary

- **Construction Forms Found:** ${analysisResults.construction_forms.length}
- **Database Constraints:** ${analysisResults.database_constraints.length}
- **Service Type Usage Patterns:** ${analysisResults.service_type_usage.length}
- **Potential Issues:** ${analysisResults.potential_issues.length}
- **Critical Issues:** ${analysisResults.potential_issues.filter(i => i.severity === 'critical').length}

## Construction Forms Analysis

${analysisResults.construction_forms.map((form, index) => `
### ${index + 1}. ${form.file}

**Service Type Matches:**
${form.service_type_matches.length > 0 ? form.service_type_matches.map(match => `
- Pattern: \`${match.pattern}\`
- Match: \`${match.match}\`
- Line: ${match.lineNumber}
`).join('') : 'No service type matches found'}

**Potential Issues:**
${form.issues.length > 0 ? form.issues.map(issue => `- ${issue}`).join('\n') : 'No issues detected'}
`).join('\n')}

## Database Constraints Analysis

${analysisResults.database_constraints.map((constraint, index) => `
### ${index + 1}. ${constraint.migration}

**Supported Service Types:**
${constraint.service_types.map(type => `- \`${type}\``).join('\n')}

**Construction Support:**
- Has 'construction': ${constraint.has_construction ? '✅' : '❌'}
- Has 'residential_post_construction': ${constraint.has_residential_post_construction ? '✅' : '❌'}
`).join('\n')}

## Service Type Usage Patterns

${analysisResults.service_type_usage.map((usage, index) => `
### ${index + 1}. ${usage.file}

**Construction-Related Types:**
${usage.service_types.map(type => `- \`${type}\``).join('\n')}
`).join('\n')}

## Potential Issues

${analysisResults.potential_issues.map((issue, index) => `
### ${index + 1}. ${issue.issue} (${issue.severity.toUpperCase()})

**Description:** ${issue.description}
`).join('\n')}

## Recommendations

${analysisResults.recommendations.map((rec, index) => `
### ${index + 1}. ${rec.action} (${rec.priority.toUpperCase()} Priority)

**Description:** ${rec.description}

**Steps:**
${rec.steps.map(step => `1. ${step}`).join('\n')}
`).join('\n')}

## Key Findings Summary

Based on this static analysis:

1. **Form-Database Alignment:** ${analysisResults.potential_issues.filter(i => i.issue.includes('constraint')).length > 0 ? 'Issues detected between forms and database constraints' : 'Forms appear aligned with database constraints'}

2. **Service Type Consistency:** ${analysisResults.construction_forms.filter(f => f.issues.includes('conflicting service types')).length > 0 ? 'Some forms may have conflicting service types' : 'Service types appear consistent'}

3. **Implementation Completeness:** ${analysisResults.construction_forms.filter(f => f.issues.includes('does not submit data')).length > 0 ? 'Some forms may not be properly submitting data' : 'Forms appear to have submission handling'}

## Next Steps

1. **Immediate Actions:** Address critical issues related to database constraints
2. **Code Review:** Review flagged forms for proper service type handling
3. **Testing:** Run end-to-end tests to verify form submission works
4. **Database Verification:** Check actual database state to confirm data storage

---
*This report was generated by static code analysis*
*For complete verification, database connectivity and runtime testing are recommended*
`;
  
  fs.writeFileSync(ANALYSIS_CONFIG.outputFile, report, 'utf8');
}

/**
 * Main analysis function
 */
async function runStaticAnalysis() {
  console.log('🔍 STARTING STATIC CONSTRUCTION FORM ANALYSIS...\n');
  
  try {
    // Run analysis steps
    analyzeConstructionForms();
    analyzeDatabaseConstraints();
    analyzeServiceTypeUsage();
    identifyPotentialIssues();
    generateRecommendations();
    
    // Write report
    await writeAnalysisReport();
    
    console.log('\n✅ STATIC ANALYSIS COMPLETE!');
    console.log(`📋 Report written to: ${ANALYSIS_CONFIG.outputFile}`);
    
    // Summary
    console.log('\n📊 Analysis Summary:');
    console.log(`- Construction forms: ${analysisResults.construction_forms.length}`);
    console.log(`- Database constraints: ${analysisResults.database_constraints.length}`);
    console.log(`- Potential issues: ${analysisResults.potential_issues.length}`);
    console.log(`- Critical issues: ${analysisResults.potential_issues.filter(i => i.severity === 'critical').length}`);
    
    return analysisResults;
    
  } catch (error) {
    console.error('❌ Static analysis failed:', error.message);
    throw error;
  }
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
  runStaticAnalysis()
    .then((results) => {
      const criticalIssues = results.potential_issues.filter(i => i.severity === 'critical').length;
      process.exit(criticalIssues > 0 ? 1 : 0);
    })
    .catch((error) => {
      console.error('💥 Analysis failed:', error.message);
      process.exit(1);
    });
}

export { runStaticAnalysis };