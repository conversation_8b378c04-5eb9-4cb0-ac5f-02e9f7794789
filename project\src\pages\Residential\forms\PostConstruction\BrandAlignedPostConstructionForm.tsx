import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Sparkles, CheckCircle, ArrowRight, ChevronLeft,
  Building2, Warehouse, Wind, Sun, Paintbrush2, 
  HardHat, Brush, Trash2, CreditCard, Shield, AlertCircle
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import GlassmorphismSelect from '../../../../components/ui/GlassmorphismSelect';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';
import { calculatePrice, type PricingInput } from '../../../../lib/services/pricingService';
import { normalizeServiceType } from '../../../../lib/services/serviceTypeRegistry';
import { ServiceTypeStandardizer } from '../../../../lib/services/serviceTypeStandardizer';
import { supabase } from '../../../../lib/supabase/client';

interface FormData {
  propertyType: string;
  propertySize: string;
  cleaningServices: string[];
  constructionType: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  preferredStartDate: string;
  cleaningTimeline: string;
  specialInstructions: string;
  howDidYouHear: string;
  newsletter: boolean;
  serviceType: string;
}

interface ValidationErrors {
  [key: string]: string;
}

const BrandAlignedPostConstructionForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<FormData>>({
    propertyType: '',
    propertySize: '',
    cleaningServices: [],
    constructionType: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    preferredStartDate: '',
    cleaningTimeline: '',
    specialInstructions: '',
    howDidYouHear: '',
    newsletter: false,
    serviceType: 'residential_post_construction'
  });
  
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  // Save form data to localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('postConstructionCleaningFormData');
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        setFormData(parsed);
        localStorage.removeItem('postConstructionCleaningFormData');
      } catch (error) {
        console.error('Error parsing saved form data:', error);
      }
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('postConstructionCleaningFormData', JSON.stringify(formData));
  }, [formData]);

  const steps = [
    { id: 1, name: 'Property Details' },
    { id: 2, name: 'Service Schedule' },
    { id: 3, name: 'Add-ons' },
    { id: 4, name: 'Contact & Schedule' },
  ];

  // Property types for post-construction cleaning
  const propertyTypes = [
    { 
      id: 'apartment', 
      name: 'Apartment/Condo', 
      icon: Building2, 
      description: 'Studio to 3BR units'
    },
    { 
      id: 'house', 
      name: 'Single Family House', 
      icon: Home, 
      description: '1-2 story homes'
    },
    { 
      id: 'townhouse', 
      name: 'Townhouse', 
      icon: Warehouse, 
      description: 'Multi-level attached'
    },
    { 
      id: 'large-home', 
      name: 'Large Home/Estate', 
      icon: HardHat, 
      description: '3+ stories, 4+ bedrooms'
    }
  ];

  // Property sizes for cleaning estimates
  const propertySizes = [
    { id: 'small', name: 'Small (< 1,200 sq ft)', description: 'Starting at $200', estimateRange: '$200 - $280' },
    { id: 'medium', name: 'Medium (1,200 - 2,000 sq ft)', description: 'Starting at $250', estimateRange: '$250 - $350' },
    { id: 'large', name: 'Large (2,000 - 3,000 sq ft)', description: 'Starting at $350', estimateRange: '$350 - $450' },
    { id: 'xl', name: 'Extra Large (3,000+ sq ft)', description: 'Starting at $450', estimateRange: '$450+' }
  ];

  // Construction types that need cleaning
  const constructionTypes = [
    {
      id: 'renovation',
      name: 'Home Renovation',
      description: 'Kitchen, bathroom, or room remodel',
      icon: Paintbrush2,
      popular: true
    },
    {
      id: 'new-construction',
      name: 'New Construction',
      description: 'Brand new home or addition',
      icon: HardHat,
      popular: true
    },
    {
      id: 'flooring',
      name: 'Flooring Installation',
      description: 'Hardwood, tile, carpet installation',
      icon: Home,
      popular: true
    },
    {
      id: 'painting',
      name: 'Painting Project',
      description: 'Interior or exterior painting',
      icon: Brush
    },
    {
      id: 'roofing',
      name: 'Roofing Work',
      description: 'Roof replacement or repair',
      icon: Home
    },
    {
      id: 'other',
      name: 'Other Construction',
      description: 'Electrical, plumbing, HVAC work',
      icon: Wind
    }
  ];

  // Post-construction cleaning services
  const cleaningServices = [
    {
      id: 'dust-removal',
      name: 'Dust & Debris Removal',
      description: 'Complete dust removal from all surfaces',
      icon: Wind,
      popular: true
    },
    {
      id: 'window-cleaning',
      name: 'Window Cleaning',
      description: 'Interior and exterior window cleaning',
      icon: Sparkles,
      popular: true
    },
    {
      id: 'floor-cleaning',
      name: 'Floor Deep Cleaning',
      description: 'Deep cleaning of all floor surfaces',
      icon: Home,
      popular: true
    },
    {
      id: 'paint-removal',
      name: 'Paint Splatter Removal',
      description: 'Remove paint drops and splatters',
      icon: Paintbrush2,
      popular: true
    },
    {
      id: 'fixture-cleaning',
      name: 'Fixture & Appliance Cleaning',
      description: 'Clean all fixtures, appliances, and hardware',
      icon: Sun,
      popular: true
    },
    {
      id: 'cabinet-cleaning',
      name: 'Cabinet Interior Cleaning',
      description: 'Clean inside cabinets and drawers',
      icon: Building2
    },
    {
      id: 'baseboard-cleaning',
      name: 'Baseboard & Trim Cleaning',
      description: 'Detailed cleaning of baseboards and trim',
      icon: Brush
    },
    {
      id: 'debris-removal',
      name: 'Construction Debris Removal',
      description: 'Remove leftover construction materials',
      icon: Trash2
    }
  ];

  const timelineOptions = [
    { id: 'asap', name: 'As Soon As Possible' },
    { id: '1-week', name: 'Within 1 Week' },
    { id: '2-weeks', name: 'Within 2 Weeks' },
    { id: 'flexible', name: 'Flexible Timeline' }
  ];

  // Calculate total price based on current form data
  const calculateTotalPrice = (): number => {
    try {
      const normalizedServiceType = normalizeServiceType(formData.serviceType || 'residential_post_construction');
      
      const pricingInput: PricingInput = {
        serviceType: normalizedServiceType,
        propertySize: formData.propertySize,
        addOns: formData.cleaningServices || [],
        urgency: formData.cleaningTimeline === 'asap' ? 'urgent' : 'standard'
      };

      const pricingResult = calculatePrice(pricingInput);
      return pricingResult.total;
    } catch (error) {
      console.error('Error calculating price:', error);
      return 250; // Base price fallback
    }
  };

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10,}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  const validateZipCode = (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  };

  const validateField = (field: string, value: string): string => {
    switch (field) {
      case 'firstName':
      case 'lastName':
        return value.length < 2 ? 'Must be at least 2 characters' : '';
      case 'email':
        return !validateEmail(value) ? 'Please enter a valid email address' : '';
      case 'phone':
        return !validatePhone(value) ? 'Please enter a valid 10-digit phone number' : '';
      case 'address':
        return value.length < 5 ? 'Please enter a complete address' : '';
      case 'city':
        return value.length < 2 ? 'Please enter a valid city' : '';
      case 'zipCode':
        return !validateZipCode(value) ? 'Please enter a valid ZIP code (12345 or 12345-6789)' : '';
      default:
        return '';
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Real-time validation
    const error = validateField(field, value);
    setValidationErrors(prev => ({
      ...prev,
      [field]: error
    }));
  };

  // Step validation
  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.propertyType && formData.propertySize);
      case 2:
        return !!(formData.constructionType && formData.preferredStartDate && formData.cleaningTimeline);
      case 3:
        return !!(formData.cleaningServices && formData.cleaningServices.length > 0);
      case 4: {
        const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'zipCode'];
        return requiredFields.every(field => {
          const value = formData[field as keyof FormData] as string;
          return value && !validateField(field, value);
        });
      }
      default:
        return false;
    }
  };

  // Enhanced form submission with better feedback - follows successful Square payment patterns
  const handleSubmit = async () => {
    // Validate current step
    if (!isStepValid(4)) {
      setValidationErrors({
        general: 'Please fill in all required fields before proceeding.'
      });
      return;
    }
    
    // Check user authentication
    if (!user) {
      // Enhanced user feedback instead of alert
      setValidationErrors({
        auth: 'Please log in to continue with your booking and payment.'
      });
      setTimeout(() => navigate('/auth/login'), 2000);
      return;
    }
    
    setIsSubmitting(true);
    setValidationErrors({}); // Clear any existing errors
    
    try {
      console.log('🎯 Post-Construction Form: Starting submission process');
      
      // Validate form data completeness
      const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'zipCode'] as const;
      const missingFields = requiredFields.filter(field => !formData[field as keyof FormData]);
      
      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
      }

      // Standardize the form data before saving (same pattern as working forms)
      const standardizedFormData = ServiceTypeStandardizer.standardizeFormServiceType({
        ...formData,
        serviceType: 'residential_post_construction',
        propertyType: formData.propertyType || 'residential',
        totalPrice: calculateTotalPrice(),
        submittedAt: new Date().toISOString(),
        source: 'residential_post_construction_form'
      });

      console.log('✅ Form data standardized for payment:', {
        serviceType: standardizedFormData.serviceType,
        totalPrice: standardizedFormData.totalPrice,
        hasContact: !!standardizedFormData.email
      });

      // Standardize and validate service type using enhanced backend infrastructure
      const serviceType = ServiceTypeStandardizer.prepareServiceTypeForDb('residential_post_construction');
      
      // Validate service type before proceeding
      if (!ServiceTypeStandardizer.isValid(serviceType)) {
        throw new Error(`Invalid residential service type: ${serviceType}`);
      }
      
      // Prepare booking data for database (save as estimate request)
      const rawBookingData = {
        user_id: user?.id,
        service_type: serviceType,
        status: 'quote_requested',
        contact: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone
        },
        property_details: {
          type: formData.propertyType || 'residential',
          size: formData.propertySize,
          address: formData.address,
          city: formData.city,
          zipCode: formData.zipCode
        },
        service_details: {
          constructionType: formData.constructionType,
          cleaningServices: formData.cleaningServices || [],
          cleaningTimeline: formData.cleaningTimeline,
          serviceSubType: 'residential_post_construction',
          totalPrice: calculateTotalPrice(),
          actualServiceType: 'residential_post_construction',
          specialInstructions: formData.specialInstructions || '',
          submittedAt: new Date().toISOString(),
          source: 'brand_aligned_post_construction_form',
          requestType: 'estimate'
        },
        schedule: {
          preferredDate: formData.preferredStartDate,
          cleaningTimeline: formData.cleaningTimeline
        }
      };

      // Standardize the booking data for database insertion
      const bookingData = ServiceTypeStandardizer.standardizeBookingData(rawBookingData);

      console.log('✅ Residential Post-Construction: Attempting to save estimate with standardized data:', {
        service_type: bookingData.service_type,
        originalServiceType: bookingData.metadata?.originalServiceType,
        standardizedAt: bookingData.metadata?.standardizedAt,
        status: bookingData.status
      });

      // Use imported supabase client
      if (!supabase) {
        throw new Error('Database connection not available. Please try again later.');
      }
      
      // Save to database as estimate request
      const { data: savedBooking, error } = await supabase
        .from('booking_forms')
        .insert([bookingData])
        .select()
        .single();

      if (error) {
        console.error('Detailed error saving post-construction booking:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      console.log('✅ Residential Post-Construction estimate saved successfully:', savedBooking);
      
      // Clear form data from localStorage after successful submission
      localStorage.removeItem('postConstructionCleaningFormData');
      
      // Navigate to thank you page
      navigate('/thank-you', { 
        state: { 
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: savedBooking.id,
            confirmationNumber: `PC-${savedBooking.id}`,
            emailSent: true
          },
          serviceType: 'Post-Construction Cleaning',
          bookingDetails: {
            id: savedBooking.id,
            type: 'Post-Construction Cleaning',
            serviceType: 'Post-Construction Cleaning',
            status: 'quote_requested',
            message: `Your post-construction cleaning estimate has been submitted successfully! You'll receive a quote within 24 hours.`
          }
        }
      });
      
    } catch (error) {
      console.error('❌ Residential Post-Construction Form submission error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Please try again.';
      setValidationErrors({
        submission: `Unable to process your residential post-construction cleaning request: ${errorMessage}`
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle successful payment (same pattern as ModernRegularCleaningForm)
  const handlePaymentComplete = async () => {
    setShowPaymentModal(false);
    
    try {
      // Standardize and validate service type for database using enhanced backend infrastructure
      const serviceType = ServiceTypeStandardizer.prepareServiceTypeForDb('residential_post_construction');
      
      // Validate service type before proceeding
      if (!ServiceTypeStandardizer.isValid(serviceType)) {
        throw new Error(`Invalid residential service type for payment: ${serviceType}`);
      }

      // Prepare standardized booking data for database (same pattern as working forms)
      const rawBookingData = {
        user_id: user?.id,
        service_type: serviceType,
        status: 'pending',
        contact: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone
        },
        property_details: {
          type: formData.propertyType,
          size: formData.propertySize,
          address: formData.address,
          city: formData.city,
          zipCode: formData.zipCode
        },
        service_details: {
          constructionType: formData.constructionType,
          cleaningServices: formData.cleaningServices || [],
          cleaningTimeline: formData.cleaningTimeline,
          serviceSubType: serviceType,
          totalPrice: calculateTotalPrice(),
          actualServiceType: serviceType,
          specialInstructions: formData.specialInstructions || '',
          submittedAt: new Date().toISOString(),
          source: 'brand_aligned_post_construction_form'
        },
        schedule: {
          preferredDate: formData.preferredStartDate,
          cleaningTimeline: formData.cleaningTimeline
        }
      };

      // Standardize the booking data for database insertion (same as working forms)
      const bookingData = ServiceTypeStandardizer.standardizeBookingData(rawBookingData);

      console.log('✅ Residential Post-Construction Payment: Attempting to save booking with standardized data:', {
        service_type: bookingData.service_type,
        originalServiceType: bookingData.metadata?.originalServiceType,
        status: bookingData.status
      });

      // Save to database (same as working forms)
      const { data: savedBooking, error } = await supabase!
        .from('booking_forms')
        .insert([bookingData])
        .select()
        .single();

      if (error) {
        console.error('Detailed error saving booking:', error);
        throw new Error(`Failed to save booking to database: ${error.message}`);
      }

      console.log('Booking saved successfully:', savedBooking);

      // Clear localStorage since booking is now saved
      localStorage.removeItem('postConstructionBookingData');
      
      // Navigate to Thank You page with booking data (same as working forms)
      navigate('/thank-you', { 
        state: { 
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: savedBooking.id,
            confirmationNumber: `PC-${savedBooking.id}`,
            emailSent: true
          },
          paymentStatus: 'paid',
          serviceType: 'Post-Construction Cleaning',
          bookingDetails: {
            id: savedBooking.id,
            type: 'Post-Construction Cleaning',
            serviceType: 'Post-Construction Cleaning',
            status: 'confirmed',
            message: `Your post-construction cleaning service has been booked successfully! You'll receive a confirmation email shortly.`
          }
        }
      });
    } catch (error) {
      console.error('Payment completion error:', error);
      // Still navigate to thank you page on payment completion
      navigate('/thank-you', {
        state: {
          formData: formData,
          serviceType: 'Post-Construction Cleaning',
          message: 'Thank you for your payment! Your post-construction cleaning service has been booked.'
        }
      });
    }
  };

  const handleServiceToggle = (serviceId: string) => {
    const currentServices = formData.cleaningServices || [];
    if (currentServices.includes(serviceId)) {
      setFormData({
        ...formData,
        cleaningServices: currentServices.filter(id => id !== serviceId)
      });
    } else {
      setFormData({
        ...formData,
        cleaningServices: [...currentServices, serviceId]
      });
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-6xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <div className="flex items-center justify-center gap-3 mb-4">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              >
                <HardHat className="w-10 h-10 text-green-400" />
              </motion.div>
              <h1 className="text-3xl sm:text-4xl font-bold text-gray-900">
                Post-Construction Cleaning
              </h1>
            </div>
            <p className="text-gray-600">Professional cleaning service after your renovation or construction project is complete.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${currentStep >= step.id ? 'bg-emerald-800 text-white shadow-md' : 'bg-white border-2 border-gray-200 text-gray-600'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} className="text-white" /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-gray-900 font-medium' : 'text-gray-500'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-emerald-800 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Form */}
            <div className="lg:col-span-2">
              <motion.div className="bg-white border border-gray-200 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-200" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
                <AnimatePresence mode="wait">
                  {/* Step 1: Property Details */}
                  {currentStep === 1 && (
                    <motion.div key="step1" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                      <h2 className="text-2xl font-bold text-gray-900 mb-6">Tell us about your property</h2>
                      
                      {/* Property Type Selection */}
                      <div className="mb-6">
                        <label className="block text-sm font-semibold text-gray-900 mb-4">Property Type</label>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          {propertyTypes.map((type) => {
                            const IconComponent = type.icon;
                            return (
                              <motion.button
                                key={type.id}
                                whileHover={{ scale: 1.02, y: -2 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={() => setFormData({ ...formData, propertyType: type.id, propertySize: '' })}
                                className={`relative p-4 rounded-2xl border-2 transition-all text-center duration-200 ${
                                  formData.propertyType === type.id
                                    ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100'
                                    : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                                }`}
                              >
                                <div className={`mb-2 flex justify-center p-2 rounded-lg transition-colors duration-200 ${
                                  formData.propertyType === type.id ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                                }`}>
                                  <IconComponent className="w-6 h-6" />
                                </div>
                                <h3 className="font-semibold text-gray-900 text-sm">{type.name}</h3>
                                <p className="text-xs text-gray-600 mt-1">{type.description}</p>
                              </motion.button>
                            );
                          })}
                        </div>
                      </div>

                      {/* Property Size Selection */}
                      {formData.propertyType && (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mb-8"
                        >
                          <label className="block text-sm font-semibold text-gray-900 mb-4">Property Size</label>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {propertySizes.map((size) => (
                              <motion.button
                                key={size.id}
                                whileHover={{ scale: 1.02, y: -2 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={() => setFormData({ ...formData, propertySize: size.id })}
                                className={`relative p-6 rounded-2xl border-2 transition-all text-left duration-200 ${
                                  formData.propertySize === size.id
                                    ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100'
                                    : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                                }`}
                              >
                                <div className="flex items-start gap-4">
                                  <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                    formData.propertySize === size.id ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                                  }`}>
                                    <Home className="w-6 h-6" />
                                  </div>
                                  <div className="flex-1">
                                    <div className="flex items-start justify-between mb-2">
                                      <h3 className="font-semibold text-gray-900 text-lg">{size.name}</h3>
                                      <span className="text-lg font-bold text-emerald-800 bg-emerald-100 px-2 py-1 rounded-lg">{size.estimateRange}</span>
                                    </div>
                                    <p className="text-sm text-gray-600 mb-1">{size.description}</p>
                                    <p className="text-xs text-gray-500">Estimated price range</p>
                                  </div>
                                </div>
                              </motion.button>
                            ))}
                          </div>
                        </motion.div>
                      )}

                      <div className="flex justify-end">
                        <Button 
                          onClick={() => setCurrentStep(2)} 
                          disabled={!isStepValid(1)}
                          className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Continue <ArrowRight className="ml-2 h-5 w-5 text-white" />
                        </Button>
                      </div>
                    </motion.div>
                  )}

                  {/* Step 2: Service Schedule */}
                  {currentStep === 2 && (
                    <motion.div key="step2" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                      <h2 className="text-2xl font-bold text-gray-900 mb-6">What type of construction was completed?</h2>
                      
                      {/* Construction Type */}
                      <div className="mb-6">
                        <label className="block text-sm font-semibold text-gray-900 mb-4">Construction/Renovation Type</label>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                          {constructionTypes.map((type) => {
                            const IconComponent = type.icon;
                            return (
                              <motion.button
                                key={type.id}
                                whileHover={{ scale: 1.02, y: -2 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={() => setFormData({ ...formData, constructionType: type.id })}
                                className={`relative p-4 rounded-2xl border-2 transition-all text-center duration-200 ${
                                  formData.constructionType === type.id
                                    ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100'
                                    : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                                }`}
                              >
                                <div className={`mb-2 flex justify-center p-2 rounded-lg transition-colors duration-200 ${
                                  formData.constructionType === type.id ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                                }`}>
                                  <IconComponent className="w-6 h-6" />
                                </div>
                                <h3 className="font-semibold text-gray-900 text-sm">{type.name}</h3>
                                <p className="text-xs text-gray-600 mt-1">{type.description}</p>
                                {type.popular && (
                                  <div className="absolute -top-2 -right-2 bg-emerald-600 text-white text-xs px-2 py-1 rounded-full">
                                    Popular
                                  </div>
                                )}
                              </motion.button>
                            );
                          })}
                        </div>
                      </div>

                      {/* Scheduling Section */}
                      <div className="mb-8 p-6 bg-gray-50 rounded-2xl border border-gray-200 shadow-sm">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                          <Sun className="w-5 h-5 text-emerald-600" />
                          Schedule Your Cleaning
                        </h3>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          {/* Preferred Start Date */}
                          <div>
                            <label className="block text-sm font-semibold text-gray-900 mb-3">Preferred Start Date *</label>
                            <input
                              type="date"
                              value={formData.preferredStartDate || ''}
                              onChange={(e) => handleInputChange('preferredStartDate', e.target.value)}
                              className="w-full p-4 bg-white border border-gray-200 rounded-xl text-gray-900 focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md"
                              min={new Date().toISOString().split('T')[0]}
                            />
                          </div>

                          {/* Timeline */}
                          <div>
                            <label className="block text-sm font-semibold text-gray-900 mb-3">Timeline *</label>
                            <GlassmorphismSelect
                              value={formData.cleaningTimeline || ''}
                              onChange={(value) => setFormData({ ...formData, cleaningTimeline: value })}
                              options={timelineOptions.map(option => ({ id: option.id, name: option.name }))}
                              placeholder="Select timeline"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Special Instructions */}
                      <div className="mb-8">
                        <label className="block text-sm font-semibold text-gray-900 mb-3">
                          Special Instructions
                          <span className="text-gray-500 font-normal ml-2">(Optional)</span>
                        </label>
                        <textarea
                          value={formData.specialInstructions || ''}
                          onChange={(e) => handleInputChange('specialInstructions', e.target.value)}
                          placeholder="Any specific areas that need extra attention, access instructions, or special requirements..."
                          className="w-full p-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md resize-none"
                          rows={4}
                        />
                        <p className="text-xs text-gray-500 mt-2">
                          💡 Include details about dust-heavy areas, delicate surfaces, or access requirements
                        </p>
                      </div>

                      <div className="flex justify-between">
                        <Button 
                          onClick={() => setCurrentStep(1)} 
                          className="bg-white/10 hover:bg-white/20 text-white border border-white/20 font-semibold rounded-xl px-6 py-3 transition-all duration-300"
                        >
                          <ChevronLeft className="mr-2 h-5 w-5" /> Back
                        </Button>
                        <Button 
                          onClick={() => setCurrentStep(3)} 
                          disabled={!isStepValid(2)}
                          className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Continue <ArrowRight className="ml-2 h-5 w-5 text-white" />
                        </Button>
                      </div>
                    </motion.div>
                  )}

                  {/* Step 3: Add-ons */}
                  {currentStep === 3 && (
                    <motion.div key="step3" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                      <h2 className="text-2xl font-bold text-gray-900 mb-6">Select cleaning services needed</h2>
                      <p className="text-gray-600 mb-6">Choose the post-construction cleaning services you need</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                        {cleaningServices.map((service) => {
                          const IconComponent = service.icon;
                          const isSelected = formData.cleaningServices?.includes(service.id);
                          
                          return (
                            <motion.button
                              key={service.id}
                              whileHover={{ scale: 1.02, y: -2 }}
                              whileTap={{ scale: 0.98 }}
                              onClick={() => handleServiceToggle(service.id)}
                              className={`relative p-6 rounded-2xl border-2 transition-all text-left duration-200 ${
                                isSelected
                                  ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100'
                                  : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                              }`}
                            >
                              <div className="flex items-start gap-4">
                                <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                  isSelected ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                                }`}>
                                  <IconComponent className="w-6 h-6" />
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-start justify-between mb-2">
                                    <h3 className="font-semibold text-gray-900 text-lg">{service.name}</h3>
                                    {isSelected && (
                                      <CheckCircle className="w-6 h-6 text-emerald-600" />
                                    )}
                                  </div>
                                  <p className="text-sm text-gray-600">{service.description}</p>
                                </div>
                              </div>
                              {service.popular && (
                                <div className="absolute -top-2 -right-2 bg-emerald-600 text-white text-xs px-2 py-1 rounded-full">
                                  Popular
                                </div>
                              )}
                            </motion.button>
                          );
                        })}
                      </div>

                      <div className="flex justify-between">
                        <Button 
                          onClick={() => setCurrentStep(2)} 
                          className="bg-white/10 hover:bg-white/20 text-white border border-white/20 font-semibold rounded-xl px-6 py-3 transition-all duration-300"
                        >
                          <ChevronLeft className="mr-2 h-5 w-5" /> Back
                        </Button>
                        <Button 
                          onClick={() => setCurrentStep(4)} 
                          disabled={!isStepValid(3)}
                          className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Continue <ArrowRight className="ml-2 h-5 w-5 text-white" />
                        </Button>
                      </div>
                    </motion.div>
                  )}

                  {/* Step 4: Contact Information */}
                  {currentStep === 4 && (
                    <motion.div key="step4" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                      <h2 className="text-2xl font-bold text-gray-900 mb-6">Contact Information</h2>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                          <label className="block text-sm font-semibold text-gray-900 mb-3">First Name *</label>
                          <input
                            type="text"
                            value={formData.firstName || ''}
                            onChange={(e) => handleInputChange('firstName', e.target.value)}
                            className="w-full p-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md"
                            placeholder="Enter first name"
                          />
                          {validationErrors.firstName && (
                            <p className="text-red-400 text-sm mt-1">{validationErrors.firstName}</p>
                          )}
                        </div>
                        
                        <div>
                          <label className="block text-sm font-semibold text-gray-900 mb-3">Last Name *</label>
                          <input
                            type="text"
                            value={formData.lastName || ''}
                            onChange={(e) => handleInputChange('lastName', e.target.value)}
                            className="w-full p-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md"
                            placeholder="Enter last name"
                          />
                          {validationErrors.lastName && (
                            <p className="text-red-400 text-sm mt-1">{validationErrors.lastName}</p>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                          <label className="block text-sm font-semibold text-gray-900 mb-3">Email *</label>
                          <input
                            type="email"
                            value={formData.email || ''}
                            onChange={(e) => handleInputChange('email', e.target.value)}
                            className="w-full p-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md"
                            placeholder="Enter email address"
                          />
                          {validationErrors.email && (
                            <p className="text-red-400 text-sm mt-1">{validationErrors.email}</p>
                          )}
                        </div>
                        
                        <div>
                          <label className="block text-sm font-semibold text-gray-900 mb-3">Phone *</label>
                          <input
                            type="tel"
                            value={formData.phone || ''}
                            onChange={(e) => handleInputChange('phone', e.target.value)}
                            className="w-full p-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md"
                            placeholder="Enter phone number"
                          />
                          {validationErrors.phone && (
                            <p className="text-red-400 text-sm mt-1">{validationErrors.phone}</p>
                          )}
                        </div>
                      </div>

                      <div className="mb-6">
                        <label className="block text-sm font-semibold text-gray-900 mb-3">Address *</label>
                        <input
                          type="text"
                          value={formData.address || ''}
                          onChange={(e) => handleInputChange('address', e.target.value)}
                          className="w-full p-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md"
                          placeholder="Enter street address"
                        />
                        {validationErrors.address && (
                          <p className="text-red-400 text-sm mt-1">{validationErrors.address}</p>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                          <label className="block text-sm font-semibold text-gray-900 mb-3">City *</label>
                          <input
                            type="text"
                            value={formData.city || ''}
                            onChange={(e) => handleInputChange('city', e.target.value)}
                            className="w-full p-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md"
                            placeholder="Enter city"
                          />
                          {validationErrors.city && (
                            <p className="text-red-400 text-sm mt-1">{validationErrors.city}</p>
                          )}
                        </div>
                        
                        <div>
                          <label className="block text-sm font-semibold text-gray-900 mb-3">ZIP Code *</label>
                          <input
                            type="text"
                            value={formData.zipCode || ''}
                            onChange={(e) => handleInputChange('zipCode', e.target.value)}
                            className="w-full p-4 bg-white border border-gray-200 rounded-xl text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md"
                            placeholder="Enter ZIP code"
                          />
                          {validationErrors.zipCode && (
                            <p className="text-red-400 text-sm mt-1">{validationErrors.zipCode}</p>
                          )}
                        </div>
                      </div>

                      <div className="mb-6">
                        <label className="block text-sm font-semibold text-gray-900 mb-3">How did you hear about us?</label>
                        <GlassmorphismSelect
                          value={formData.howDidYouHear || ''}
                          onChange={(value) => setFormData({ ...formData, howDidYouHear: value })}
                          options={[
                            { id: 'google', name: 'Google Search' },
                            { id: 'social', name: 'Social Media' },
                            { id: 'referral', name: 'Friend/Family Referral' },
                            { id: 'contractor', name: 'Contractor Recommendation' },
                            { id: 'other', name: 'Other' }
                          ]}
                          placeholder="Select option"
                        />
                      </div>

                      <div className="mb-8">
                        <label className="flex items-center gap-3 text-gray-900 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData.newsletter || false}
                            onChange={(e) => setFormData({ ...formData, newsletter: e.target.checked })}
                            className="w-5 h-5 text-emerald-600 bg-white border-gray-300 rounded focus:ring-emerald-500"
                          />
                          <span className="text-sm">Subscribe to our newsletter for cleaning tips and special offers</span>
                        </label>
                      </div>

                      <div className="flex justify-between">
                        <Button 
                          onClick={() => setCurrentStep(3)} 
                          className="bg-white/10 hover:bg-white/20 text-white border border-white/20 font-semibold rounded-xl px-6 py-3 transition-all duration-300"
                        >
                          <ChevronLeft className="mr-2 h-5 w-5" /> Back
                        </Button>
                        
                        {/* Enhanced Booking Button */}
                        <motion.div 
                          className="relative"
                          whileHover={{ scale: !isSubmitting ? 1.05 : 1 }}
                          whileTap={{ scale: !isSubmitting ? 0.95 : 1 }}
                        >
                          <Button 
                            onClick={handleSubmit}
                            disabled={!isStepValid(4) || isSubmitting}
                            className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-bold shadow-xl rounded-xl px-8 py-4 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-3 min-w-[200px] justify-center relative overflow-hidden"
                          >
                            {/* Enhanced loading state */}
                            {isSubmitting ? (
                              <>
                                <motion.div
                                  animate={{ rotate: 360 }}
                                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                  className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full"
                                />
                                <span>Processing...</span>
                              </>
                            ) : (
                              <>
                                <motion.div
                                  animate={{ 
                                    scale: [1, 1.2, 1],
                                    rotate: [0, 10, -10, 0]
                                  }}
                                  transition={{ 
                                    duration: 2, 
                                    repeat: Infinity, 
                                    repeatDelay: 3,
                                    ease: "easeInOut"
                                  }}
                                >
                                  <CreditCard className="w-5 h-5" />
                                </motion.div>
                                <span className="font-bold text-lg">
                                  Book Now ${calculateTotalPrice()}
                                </span>
                                <motion.div
                                  animate={{ x: [0, 5, 0] }}
                                  transition={{ 
                                    duration: 1.5, 
                                    repeat: Infinity, 
                                    repeatDelay: 2,
                                    ease: "easeInOut"
                                  }}
                                >
                                  <ArrowRight className="w-5 h-5" />
                                </motion.div>
                              </>
                            )}
                            
                            {/* Animated background shimmer */}
                            {!isSubmitting && (
                              <motion.div
                                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                                animate={{ x: [-200, 200] }}
                                transition={{ 
                                  duration: 3, 
                                  repeat: Infinity, 
                                  repeatDelay: 4,
                                  ease: "easeInOut"
                                }}
                                style={{ width: '200%' }}
                              />
                            )}
                          </Button>
                          
                          {/* Success checkmark overlay */}
                          <AnimatePresence>
                            {isSubmitting && (
                              <motion.div
                                initial={{ scale: 0, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                exit={{ scale: 0, opacity: 0 }}
                                className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-lg"
                              >
                                <CheckCircle className="w-4 h-4 text-white" />
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </motion.div>
                      </div>
                      
                      {/* Enhanced Error Display */}
                      <AnimatePresence>
                        {(validationErrors.general || validationErrors.auth || validationErrors.submission) && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="mt-4 p-4 bg-red-500/10 backdrop-blur-sm rounded-xl border border-red-400/30"
                          >
                            <div className="flex items-start gap-3">
                              <AlertCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                              <div>
                                <p className="text-red-300 font-medium text-sm">
                                  {validationErrors.general || validationErrors.auth || validationErrors.submission}
                                </p>
                                {validationErrors.auth && (
                                  <p className="text-red-200 text-xs mt-1">
                                    Redirecting to login page...
                                  </p>
                                )}
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                      
                      {/* Enhanced Progress Indicator */}
                      <div className="mt-6 p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                        <div className="flex items-center justify-between text-sm text-white/80">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                            <span>Step 4 of 4 - Ready to book!</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Shield className="w-4 h-4 text-green-400" />
                            <span>Secure Payment</span>
                          </div>
                        </div>
                        
                        {/* Price breakdown preview */}
                        <div className="mt-3 pt-3 border-t border-white/10">
                          <div className="flex items-center justify-between">
                            <span className="text-white/60 text-sm">Total Service Cost:</span>
                            <span className="text-green-400 font-bold text-lg">${calculateTotalPrice()}</span>
                          </div>
                          <div className="flex items-center justify-between mt-1">
                            <span className="text-white/40 text-xs">Includes all selected services</span>
                            <span className="text-white/40 text-xs">Secure checkout via Square</span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <motion.div 
                className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6 sticky top-8"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
                  <Sparkles className="w-6 h-6 text-green-400" />
                  Why Choose Our Post-Construction Cleaning?
                </h3>
                
                <div className="space-y-4 text-sm text-white/80">
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>Specialized equipment for construction dust removal</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>Experienced team trained in post-construction cleanup</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>Safe removal of paint splatters and debris</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>Detailed cleaning of all surfaces and fixtures</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>Licensed, bonded, and insured professionals</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                    <span>100% satisfaction guarantee</span>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-green-500/10 rounded-xl border border-green-400/30">
                  <h4 className="font-semibold text-white mb-2">💡 Pro Tip</h4>
                  <p className="text-sm text-white/80">
                    Schedule your post-construction cleaning as soon as your project is complete to prevent dust and debris from settling deeper into surfaces.
                  </p>
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Enhanced Payment Modal with Square SDK Integration */}
        <PaymentOptionsModal
          isOpen={showPaymentModal}
          onClose={() => {
            console.log('💡 User closed payment modal - returning to form');
            setShowPaymentModal(false);
          }}
          amount={calculateTotalPrice()}
          description={`Post-Construction Cleaning Service${formData.constructionType ? ` - ${formData.constructionType}` : ''}`}
          customerEmail={formData.email || ''}
          formData={ServiceTypeStandardizer.standardizePaymentServiceType({
            ...formData,
            serviceType: 'residential_post_construction',
            propertyType: formData.propertyType || 'residential',
            totalPrice: calculateTotalPrice(),
            constructionType: formData.constructionType,
            cleaningServices: formData.cleaningServices || [],
            propertySize: formData.propertySize,
            source: 'residential_post_construction_form'
          })}
          user={user}
          onPaymentComplete={handlePaymentComplete}
        />
      </div>
    </AnimatedBackground>
  );
};

export default BrandAlignedPostConstructionForm; 
