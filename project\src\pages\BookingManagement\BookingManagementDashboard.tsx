/**
 * Booking Management Dashboard
 * 
 * Comprehensive dashboard for managing all bookings with:
 * - Advanced analytics
 * - Status management
 * - Bulk operations
 * - Export capabilities
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Calendar, BarChart3, Users, DollarSign, TrendingUp,
  Download, Upload, Settings, Filter, Search, Plus,
  AlertTriangle, CheckCircle, Clock, XCircle
} from 'lucide-react';
import { AdvancedBookingDisplay } from '../../components/booking/AdvancedBookingDisplay';
import { EnhancedBookingService, BookingSearchFilters } from '../../lib/services/enhancedBookingService';
import { StandardizedBookingData } from '../../lib/api/bookingService';
import { useAuth } from '../../lib/auth/AuthProvider';

interface DashboardStats {
  totalBookings: number;
  totalRevenue: number;
  averageBookingValue: number;
  bookingsByStatus: Record<string, number>;
  bookingsByService: Record<string, number>;
  revenueByMonth: Record<string, number>;
  growthRate: number;
}

export function BookingManagementDashboard() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'overview' | 'bookings' | 'analytics'>('overview');
  const [stats, setStats] = useState<DashboardStats>({
    totalBookings: 0,
    totalRevenue: 0,
    averageBookingValue: 0,
    bookingsByStatus: {},
    bookingsByService: {},
    revenueByMonth: {},
    growthRate: 0
  });
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });
  const [filters, setFilters] = useState<BookingSearchFilters>({});

  // Load dashboard statistics
  const loadStatistics = async () => {
    try {
      setLoading(true);
      const result = await EnhancedBookingService.getBookingStatistics(
        user?.id,
        dateRange
      );
      setStats(result);
    } catch (error) {
      console.error('Failed to load statistics:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadStatistics();
  }, [dateRange, user?.id]);

  // Render overview tab
  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Bookings"
          value={stats.totalBookings.toString()}
          icon={Calendar}
          color="blue"
          trend="+12%"
        />
        <MetricCard
          title="Total Revenue"
          value={`$${stats.totalRevenue.toFixed(0)}`}
          icon={DollarSign}
          color="green"
          trend="+8%"
        />
        <MetricCard
          title="Average Booking"
          value={`$${stats.averageBookingValue.toFixed(0)}`}
          icon={TrendingUp}
          color="purple"
          trend="-2%"
        />
        <MetricCard
          title="Conversion Rate"
          value="68%"
          icon={CheckCircle}
          color="emerald"
          trend="+5%"
        />
      </div>

      {/* Status Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <StatusDistributionCard stats={stats} />
        <ServiceDistributionCard stats={stats} />
      </div>

      {/* Recent Activity */}
      <RecentActivityCard />
    </div>
  );

  // Render analytics tab
  const renderAnalyticsTab = () => (
    <div className="space-y-6">
      {/* Date Range Selector */}
      <div className="bg-white/[0.08] backdrop-blur-[30px] border border-white/40 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Analytics Period</h3>
        <div className="flex gap-4">
          <div>
            <label className="block text-sm text-gray-400 mb-2">Start Date</label>
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              className="px-3 py-2 bg-white/[0.08] border border-white/40 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-emerald-500/50"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-400 mb-2">End Date</label>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              className="px-3 py-2 bg-white/[0.08] border border-white/40 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-emerald-500/50"
            />
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RevenueChartCard stats={stats} />
        <BookingTrendsCard stats={stats} />
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-emerald-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Booking Management</h1>
            <p className="text-gray-400">Manage and analyze your cleaning service bookings</p>
          </div>
          
          <div className="flex gap-4 mt-4 md:mt-0">
            <button className="px-4 py-2 bg-emerald-500/20 border border-emerald-500/40 rounded-xl text-emerald-400 hover:bg-emerald-500/30 transition-colors flex items-center gap-2">
              <Download className="w-4 h-4" />
              Export
            </button>
            <button className="px-4 py-2 bg-white/[0.08] border border-white/40 rounded-xl text-white hover:bg-white/[0.12] transition-colors flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Settings
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-white/[0.08] backdrop-blur-[30px] border border-white/40 rounded-xl p-1 mb-8">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'bookings', label: 'Bookings', icon: Calendar },
            { id: 'analytics', label: 'Analytics', icon: TrendingUp }
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id as any)}
              className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-lg transition-colors ${
                activeTab === id
                  ? 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/40'
                  : 'text-gray-400 hover:text-white hover:bg-white/[0.08]'
              }`}
            >
              <Icon className="w-5 h-5" />
              {label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'overview' && renderOverviewTab()}
          {activeTab === 'bookings' && (
            <AdvancedBookingDisplay
              userId={user?.id}
              adminMode={true}
              initialFilters={filters}
              onBookingSelect={(booking) => console.log('Selected booking:', booking)}
              onBookingUpdate={(id, updates) => console.log('Updated booking:', id, updates)}
            />
          )}
          {activeTab === 'analytics' && renderAnalyticsTab()}
        </motion.div>
      </div>
    </div>
  );
}

// Metric Card Component
interface MetricCardProps {
  title: string;
  value: string;
  icon: React.ElementType;
  color: 'blue' | 'green' | 'purple' | 'emerald';
  trend?: string;
}

function MetricCard({ title, value, icon: Icon, color, trend }: MetricCardProps) {
  const colorClasses = {
    blue: 'text-blue-400 bg-blue-500/20 border-blue-500/40',
    green: 'text-green-400 bg-green-500/20 border-green-500/40',
    purple: 'text-purple-400 bg-purple-500/20 border-purple-500/40',
    emerald: 'text-emerald-400 bg-emerald-500/20 border-emerald-500/40'
  };

  return (
    <div className="bg-white/[0.08] backdrop-blur-[30px] border border-white/40 rounded-xl p-6">
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-xl ${colorClasses[color]} border`}>
          <Icon className="w-6 h-6" />
        </div>
        {trend && (
          <span className={`text-sm font-medium ${
            trend.startsWith('+') ? 'text-green-400' : 'text-red-400'
          }`}>
            {trend}
          </span>
        )}
      </div>
      <div className="text-2xl font-bold text-white mb-1">{value}</div>
      <div className="text-sm text-gray-400">{title}</div>
    </div>
  );
}

// Status Distribution Card
function StatusDistributionCard({ stats }: { stats: DashboardStats }) {
  const statusIcons = {
    pending: Clock,
    confirmed: CheckCircle,
    in_progress: TrendingUp,
    completed: CheckCircle,
    cancelled: XCircle
  };

  const statusColors = {
    pending: 'text-yellow-400',
    confirmed: 'text-green-400',
    in_progress: 'text-blue-400',
    completed: 'text-emerald-400',
    cancelled: 'text-red-400'
  };

  return (
    <div className="bg-white/[0.08] backdrop-blur-[30px] border border-white/40 rounded-xl p-6">
      <h3 className="text-lg font-semibold text-white mb-4">Booking Status</h3>
      <div className="space-y-3">
        {Object.entries(stats.bookingsByStatus).map(([status, count]) => {
          const Icon = statusIcons[status as keyof typeof statusIcons] || Clock;
          const colorClass = statusColors[status as keyof typeof statusColors] || 'text-gray-400';
          const percentage = stats.totalBookings > 0 ? (count / stats.totalBookings * 100).toFixed(1) : '0';
          
          return (
            <div key={status} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Icon className={`w-5 h-5 ${colorClass}`} />
                <span className="text-white capitalize">{status.replace('_', ' ')}</span>
              </div>
              <div className="text-right">
                <div className="text-white font-medium">{count}</div>
                <div className="text-gray-400 text-sm">{percentage}%</div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Service Distribution Card
function ServiceDistributionCard({ stats }: { stats: DashboardStats }) {
  return (
    <div className="bg-white/[0.08] backdrop-blur-[30px] border border-white/40 rounded-xl p-6">
      <h3 className="text-lg font-semibold text-white mb-4">Service Types</h3>
      <div className="space-y-3">
        {Object.entries(stats.bookingsByService).map(([service, count]) => {
          const percentage = stats.totalBookings > 0 ? (count / stats.totalBookings * 100).toFixed(1) : '0';
          
          return (
            <div key={service} className="flex items-center justify-between">
              <span className="text-white capitalize">{service.replace(/_/g, ' ')}</span>
              <div className="text-right">
                <div className="text-white font-medium">{count}</div>
                <div className="text-gray-400 text-sm">{percentage}%</div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Recent Activity Card
function RecentActivityCard() {
  return (
    <div className="bg-white/[0.08] backdrop-blur-[30px] border border-white/40 rounded-xl p-6">
      <h3 className="text-lg font-semibold text-white mb-4">Recent Activity</h3>
      <div className="space-y-4">
        {[
          { action: 'New booking created', time: '2 minutes ago', status: 'success' },
          { action: 'Booking confirmed', time: '15 minutes ago', status: 'info' },
          { action: 'Payment received', time: '1 hour ago', status: 'success' },
          { action: 'Booking cancelled', time: '2 hours ago', status: 'warning' }
        ].map((activity, index) => (
          <div key={index} className="flex items-center gap-3">
            <div className={`w-2 h-2 rounded-full ${
              activity.status === 'success' ? 'bg-green-400' :
              activity.status === 'info' ? 'bg-blue-400' :
              activity.status === 'warning' ? 'bg-yellow-400' : 'bg-gray-400'
            }`} />
            <div className="flex-1">
              <div className="text-white text-sm">{activity.action}</div>
              <div className="text-gray-400 text-xs">{activity.time}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Revenue Chart Card
function RevenueChartCard({ stats }: { stats: DashboardStats }) {
  return (
    <div className="bg-white/[0.08] backdrop-blur-[30px] border border-white/40 rounded-xl p-6">
      <h3 className="text-lg font-semibold text-white mb-4">Revenue Trends</h3>
      <div className="text-center py-8">
        <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-400">Chart visualization would go here</p>
        <p className="text-sm text-gray-500 mt-2">
          Integration with charting library like Chart.js or Recharts
        </p>
      </div>
    </div>
  );
}

// Booking Trends Card
function BookingTrendsCard({ stats }: { stats: DashboardStats }) {
  return (
    <div className="bg-white/[0.08] backdrop-blur-[30px] border border-white/40 rounded-xl p-6">
      <h3 className="text-lg font-semibold text-white mb-4">Booking Trends</h3>
      <div className="text-center py-8">
        <TrendingUp className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-400">Trend analysis would go here</p>
        <p className="text-sm text-gray-500 mt-2">
          Time series analysis of booking patterns
        </p>
      </div>
    </div>
  );
}
