/**
 * Enhanced Booking Form Component
 * 
 * Provides comprehensive booking creation with:
 * - Real-time validation
 * - Conflict detection
 * - Dynamic pricing
 * - Multi-step form flow
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Calendar, Clock, MapPin, User, Phone, Mail, Home,
  CheckCircle, AlertTriangle, DollarSign, ArrowRight,
  ArrowLeft, Save, Loader2
} from 'lucide-react';
import { BookingValidationService, ValidationError, ConflictCheck } from '../../lib/services/bookingValidationService';
import { EnhancedBookingService } from '../../lib/services/enhancedBookingService';
import { StandardizedBookingData } from '../../lib/api/bookingService';
import { useAuth } from '../../lib/auth/AuthProvider';

interface EnhancedBookingFormProps {
  serviceType: string;
  initialData?: Partial<StandardizedBookingData>;
  onSuccess?: (booking: StandardizedBookingData) => void;
  onCancel?: () => void;
}

interface FormData {
  // Contact Information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  
  // Property Details
  address: string;
  city: string;
  zipCode: string;
  propertyType: string;
  propertySize: string;
  
  // Schedule Information
  preferredDate: string;
  preferredTime: string;
  timeSlot: string;
  frequency: string;
  
  // Service Details
  servicePackage: string;
  addOns: string[];
  specialInstructions: string;
  
  // Pricing
  estimatedPrice: number;
}

interface StepConfig {
  id: number;
  title: string;
  description: string;
  fields: (keyof FormData)[];
}

export function EnhancedBookingForm({
  serviceType,
  initialData,
  onSuccess,
  onCancel
}: EnhancedBookingFormProps) {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    propertyType: 'residential',
    propertySize: '',
    preferredDate: '',
    preferredTime: '',
    timeSlot: '',
    frequency: 'one-time',
    servicePackage: 'standard',
    addOns: [],
    specialInstructions: '',
    estimatedPrice: 0
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [warnings, setWarnings] = useState<ValidationError[]>([]);
  const [conflicts, setConflicts] = useState<ConflictCheck[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form steps configuration
  const steps: StepConfig[] = [
    {
      id: 1,
      title: 'Contact Information',
      description: 'Tell us how to reach you',
      fields: ['firstName', 'lastName', 'email', 'phone']
    },
    {
      id: 2,
      title: 'Property Details',
      description: 'Where do you need cleaning?',
      fields: ['address', 'city', 'zipCode', 'propertyType', 'propertySize']
    },
    {
      id: 3,
      title: 'Service & Schedule',
      description: 'What and when do you need?',
      fields: ['preferredDate', 'preferredTime', 'servicePackage', 'addOns']
    },
    {
      id: 4,
      title: 'Review & Confirm',
      description: 'Review your booking details',
      fields: ['specialInstructions']
    }
  ];

  // Initialize form data
  useEffect(() => {
    if (initialData) {
      const mappedData: FormData = {
        firstName: initialData.contact?.firstName || '',
        lastName: initialData.contact?.lastName || '',
        email: initialData.contact?.email || '',
        phone: initialData.contact?.phone || '',
        address: initialData.property_details?.address || '',
        city: initialData.property_details?.city || '',
        zipCode: initialData.property_details?.zipCode || '',
        propertyType: initialData.property_details?.propertyType || 'residential',
        propertySize: initialData.property_details?.propertySize || '',
        preferredDate: initialData.schedule?.preferredDate || '',
        preferredTime: initialData.schedule?.preferredTime || '',
        timeSlot: initialData.schedule?.timeSlot || '',
        frequency: initialData.schedule?.frequency || 'one-time',
        servicePackage: initialData.service_details?.servicePackage || 'standard',
        addOns: initialData.service_details?.addOns || [],
        specialInstructions: initialData.special_instructions || '',
        estimatedPrice: initialData.total_price || 0
      };
      setFormData(mappedData);
    }
  }, [initialData]);

  // Real-time validation
  useEffect(() => {
    const validateCurrentStep = async () => {
      if (currentStep === 4) return; // Skip validation on review step
      
      setIsValidating(true);
      
      try {
        const bookingData = convertFormDataToBookingData(formData);
        const validation = await BookingValidationService.validateBooking(
          bookingData,
          serviceType
        );

        // Filter errors for current step fields
        const currentStepFields = steps[currentStep - 1].fields;
        const stepErrors: Record<string, string> = {};
        
        validation.errors.forEach(error => {
          if (currentStepFields.some(field => error.field.includes(field))) {
            stepErrors[error.field] = error.message;
          }
        });

        setErrors(stepErrors);
        setWarnings(validation.warnings);
        setConflicts(validation.conflicts);

      } catch (error) {
        console.error('Validation failed:', error);
      } finally {
        setIsValidating(false);
      }
    };

    const timeoutId = setTimeout(validateCurrentStep, 500);
    return () => clearTimeout(timeoutId);
  }, [formData, currentStep, serviceType]);

  // Calculate estimated price
  useEffect(() => {
    const calculatePrice = () => {
      let basePrice = 100; // Base price

      // Adjust based on service package
      const packageMultipliers = {
        basic: 0.8,
        standard: 1.0,
        premium: 1.3,
        deluxe: 1.6
      };

      basePrice *= packageMultipliers[formData.servicePackage as keyof typeof packageMultipliers] || 1.0;

      // Add-ons pricing
      const addOnPrices = {
        'deep-cleaning': 50,
        'window-cleaning': 30,
        'carpet-cleaning': 40,
        'appliance-cleaning': 25
      };

      const addOnTotal = formData.addOns.reduce((total, addOn) => {
        return total + (addOnPrices[addOn as keyof typeof addOnPrices] || 0);
      }, 0);

      const totalPrice = basePrice + addOnTotal;
      
      setFormData(prev => ({ ...prev, estimatedPrice: totalPrice }));
    };

    calculatePrice();
  }, [formData.servicePackage, formData.addOns]);

  // Handle form field changes
  const handleFieldChange = (field: keyof FormData, value: string | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear field-specific errors
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Navigate between steps
  const goToNextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Check if current step is valid
  const isCurrentStepValid = () => {
    const currentStepFields = steps[currentStep - 1].fields;
    return currentStepFields.every(field => {
      const value = formData[field];
      return value && value.toString().trim() !== '' && !errors[field];
    });
  };

  // Convert form data to booking data format
  const convertFormDataToBookingData = (data: FormData): Partial<StandardizedBookingData> => {
    return {
      user_id: user?.id,
      service_type: serviceType,
      contact: {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone
      },
      property_details: {
        address: data.address,
        city: data.city,
        zipCode: data.zipCode,
        propertyType: data.propertyType,
        propertySize: data.propertySize
      },
      schedule: {
        preferredDate: data.preferredDate,
        preferredTime: data.preferredTime,
        timeSlot: data.timeSlot,
        frequency: data.frequency
      },
      service_details: {
        serviceType,
        servicePackage: data.servicePackage,
        addOns: data.addOns,
        specialRequests: []
      },
      status: 'pending',
      total_price: data.estimatedPrice,
      special_instructions: data.specialInstructions,
      metadata: {
        submittedAt: new Date().toISOString(),
        requestType: 'booking' as const,
        source: 'enhanced_booking_form'
      }
    };
  };

  // Submit the form
  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      const bookingData = convertFormDataToBookingData(formData);
      
      const result = await EnhancedBookingService.createBooking(
        bookingData,
        {
          validateConflicts: true,
          autoResolveConflicts: false,
          userId: user?.id
        }
      );

      if (result.success && result.booking) {
        onSuccess?.(result.booking);
      } else {
        // Handle validation errors or conflicts
        if (result.errors) {
          const errorMap: Record<string, string> = {};
          result.errors.forEach(error => {
            errorMap[error.field] = error.message;
          });
          setErrors(errorMap);
        }
        
        if (result.conflicts) {
          setConflicts(result.conflicts);
        }
      }

    } catch (error) {
      console.error('Form submission failed:', error);
      setErrors({
        general: error instanceof Error ? error.message : 'Submission failed'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderContactStep();
      case 2:
        return renderPropertyStep();
      case 3:
        return renderServiceStep();
      case 4:
        return renderReviewStep();
      default:
        return null;
    }
  };

  // Render contact information step
  const renderContactStep = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            First Name *
          </label>
          <div className="relative">
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              value={formData.firstName}
              onChange={(e) => handleFieldChange('firstName', e.target.value)}
              className={`w-full pl-10 pr-4 py-3 bg-white/[0.08] border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 ${
                errors.firstName
                  ? 'border-red-500/40 focus:ring-red-500/50'
                  : 'border-white/40 focus:ring-emerald-500/50'
              }`}
              placeholder="Enter your first name"
            />
          </div>
          {errors.firstName && (
            <p className="text-red-400 text-sm mt-1">{errors.firstName}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Last Name *
          </label>
          <input
            type="text"
            value={formData.lastName}
            onChange={(e) => handleFieldChange('lastName', e.target.value)}
            className={`w-full px-4 py-3 bg-white/[0.08] border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 ${
              errors.lastName
                ? 'border-red-500/40 focus:ring-red-500/50'
                : 'border-white/40 focus:ring-emerald-500/50'
            }`}
            placeholder="Enter your last name"
          />
          {errors.lastName && (
            <p className="text-red-400 text-sm mt-1">{errors.lastName}</p>
          )}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Email Address *
        </label>
        <div className="relative">
          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="email"
            value={formData.email}
            onChange={(e) => handleFieldChange('email', e.target.value)}
            className={`w-full pl-10 pr-4 py-3 bg-white/[0.08] border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 ${
              errors.email
                ? 'border-red-500/40 focus:ring-red-500/50'
                : 'border-white/40 focus:ring-emerald-500/50'
            }`}
            placeholder="Enter your email address"
          />
        </div>
        {errors.email && (
          <p className="text-red-400 text-sm mt-1">{errors.email}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Phone Number *
        </label>
        <div className="relative">
          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) => handleFieldChange('phone', e.target.value)}
            className={`w-full pl-10 pr-4 py-3 bg-white/[0.08] border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 ${
              errors.phone
                ? 'border-red-500/40 focus:ring-red-500/50'
                : 'border-white/40 focus:ring-emerald-500/50'
            }`}
            placeholder="Enter your phone number"
          />
        </div>
        {errors.phone && (
          <p className="text-red-400 text-sm mt-1">{errors.phone}</p>
        )}
      </div>
    </div>
  );

  // Render property details step
  const renderPropertyStep = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Property Address *
        </label>
        <div className="relative">
          <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            value={formData.address}
            onChange={(e) => handleFieldChange('address', e.target.value)}
            className={`w-full pl-10 pr-4 py-3 bg-white/[0.08] border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 ${
              errors.address
                ? 'border-red-500/40 focus:ring-red-500/50'
                : 'border-white/40 focus:ring-emerald-500/50'
            }`}
            placeholder="Enter property address"
          />
        </div>
        {errors.address && (
          <p className="text-red-400 text-sm mt-1">{errors.address}</p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            City *
          </label>
          <input
            type="text"
            value={formData.city}
            onChange={(e) => handleFieldChange('city', e.target.value)}
            className={`w-full px-4 py-3 bg-white/[0.08] border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 ${
              errors.city
                ? 'border-red-500/40 focus:ring-red-500/50'
                : 'border-white/40 focus:ring-emerald-500/50'
            }`}
            placeholder="Enter city"
          />
          {errors.city && (
            <p className="text-red-400 text-sm mt-1">{errors.city}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            ZIP Code *
          </label>
          <input
            type="text"
            value={formData.zipCode}
            onChange={(e) => handleFieldChange('zipCode', e.target.value)}
            className={`w-full px-4 py-3 bg-white/[0.08] border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 ${
              errors.zipCode
                ? 'border-red-500/40 focus:ring-red-500/50'
                : 'border-white/40 focus:ring-emerald-500/50'
            }`}
            placeholder="Enter ZIP code"
          />
          {errors.zipCode && (
            <p className="text-red-400 text-sm mt-1">{errors.zipCode}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Property Type *
          </label>
          <select
            value={formData.propertyType}
            onChange={(e) => handleFieldChange('propertyType', e.target.value)}
            className="w-full px-4 py-3 bg-white/[0.08] border border-white/40 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-500/50"
          >
            <option value="residential">Residential</option>
            <option value="commercial">Commercial</option>
            <option value="office">Office</option>
            <option value="apartment">Apartment</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Property Size
          </label>
          <select
            value={formData.propertySize}
            onChange={(e) => handleFieldChange('propertySize', e.target.value)}
            className="w-full px-4 py-3 bg-white/[0.08] border border-white/40 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-500/50"
          >
            <option value="">Select size</option>
            <option value="small">Small (< 1,000 sq ft)</option>
            <option value="medium">Medium (1,000 - 2,500 sq ft)</option>
            <option value="large">Large (2,500 - 5,000 sq ft)</option>
            <option value="xl">Extra Large (> 5,000 sq ft)</option>
          </select>
        </div>
      </div>
    </div>
  );

  // Render service and schedule step
  const renderServiceStep = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Preferred Date *
          </label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="date"
              value={formData.preferredDate}
              onChange={(e) => handleFieldChange('preferredDate', e.target.value)}
              min={new Date().toISOString().split('T')[0]}
              className={`w-full pl-10 pr-4 py-3 bg-white/[0.08] border rounded-xl text-white focus:outline-none focus:ring-2 ${
                errors.preferredDate
                  ? 'border-red-500/40 focus:ring-red-500/50'
                  : 'border-white/40 focus:ring-emerald-500/50'
              }`}
            />
          </div>
          {errors.preferredDate && (
            <p className="text-red-400 text-sm mt-1">{errors.preferredDate}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Preferred Time *
          </label>
          <div className="relative">
            <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <select
              value={formData.preferredTime}
              onChange={(e) => handleFieldChange('preferredTime', e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-white/[0.08] border border-white/40 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-500/50"
            >
              <option value="">Select time</option>
              <option value="morning">Morning (8:00 AM - 12:00 PM)</option>
              <option value="afternoon">Afternoon (12:00 PM - 5:00 PM)</option>
              <option value="evening">Evening (5:00 PM - 8:00 PM)</option>
            </select>
          </div>
          {errors.preferredTime && (
            <p className="text-red-400 text-sm mt-1">{errors.preferredTime}</p>
          )}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Service Package
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[
            { value: 'basic', label: 'Basic', price: 80 },
            { value: 'standard', label: 'Standard', price: 100 },
            { value: 'premium', label: 'Premium', price: 130 },
            { value: 'deluxe', label: 'Deluxe', price: 160 }
          ].map((pkg) => (
            <button
              key={pkg.value}
              onClick={() => handleFieldChange('servicePackage', pkg.value)}
              className={`p-4 rounded-xl border transition-colors ${
                formData.servicePackage === pkg.value
                  ? 'bg-emerald-500/20 border-emerald-500/40 text-emerald-400'
                  : 'bg-white/[0.08] border-white/40 text-white hover:bg-white/[0.12]'
              }`}
            >
              <div className="text-lg font-semibold">{pkg.label}</div>
              <div className="text-sm opacity-75">${pkg.price}</div>
            </button>
          ))}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Add-On Services
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {[
            { value: 'deep-cleaning', label: 'Deep Cleaning', price: 50 },
            { value: 'window-cleaning', label: 'Window Cleaning', price: 30 },
            { value: 'carpet-cleaning', label: 'Carpet Cleaning', price: 40 },
            { value: 'appliance-cleaning', label: 'Appliance Cleaning', price: 25 }
          ].map((addon) => (
            <label
              key={addon.value}
              className="flex items-center gap-3 p-3 bg-white/[0.08] border border-white/40 rounded-xl hover:bg-white/[0.12] transition-colors cursor-pointer"
            >
              <input
                type="checkbox"
                checked={formData.addOns.includes(addon.value)}
                onChange={(e) => {
                  const newAddOns = e.target.checked
                    ? [...formData.addOns, addon.value]
                    : formData.addOns.filter(a => a !== addon.value);
                  handleFieldChange('addOns', newAddOns);
                }}
                className="w-4 h-4 text-emerald-500 rounded focus:ring-emerald-500"
              />
              <div className="flex-1">
                <div className="text-white font-medium">{addon.label}</div>
                <div className="text-gray-400 text-sm">+${addon.price}</div>
              </div>
            </label>
          ))}
        </div>
      </div>
    </div>
  );

  // Render review step
  const renderReviewStep = () => (
    <div className="space-y-6">
      {/* Price Summary */}
      <div className="bg-emerald-500/20 border border-emerald-500/40 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-4">
          <DollarSign className="w-6 h-6 text-emerald-400" />
          <h3 className="text-xl font-semibold text-emerald-400">Price Estimate</h3>
        </div>
        <div className="text-3xl font-bold text-white">
          ${formData.estimatedPrice.toFixed(2)}
        </div>
        <p className="text-emerald-300 text-sm mt-2">
          Final price may vary based on actual requirements
        </p>
      </div>

      {/* Booking Summary */}
      <div className="bg-white/[0.08] border border-white/40 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Booking Summary</h3>
        <div className="space-y-3 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-400">Service:</span>
            <span className="text-white capitalize">{serviceType.replace('_', ' ')}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Customer:</span>
            <span className="text-white">{formData.firstName} {formData.lastName}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Address:</span>
            <span className="text-white">{formData.address}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Date:</span>
            <span className="text-white">{formData.preferredDate}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Time:</span>
            <span className="text-white">{formData.preferredTime}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Package:</span>
            <span className="text-white capitalize">{formData.servicePackage}</span>
          </div>
        </div>
      </div>

      {/* Special Instructions */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Special Instructions (Optional)
        </label>
        <textarea
          value={formData.specialInstructions}
          onChange={(e) => handleFieldChange('specialInstructions', e.target.value)}
          rows={4}
          className="w-full px-4 py-3 bg-white/[0.08] border border-white/40 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500/50"
          placeholder="Any special requirements or instructions..."
        />
      </div>

      {/* Conflicts Warning */}
      {conflicts.length > 0 && (
        <div className="bg-yellow-500/20 border border-yellow-500/40 rounded-xl p-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertTriangle className="w-5 h-5 text-yellow-400" />
            <span className="font-medium text-yellow-400">Booking Conflicts Detected</span>
          </div>
          <div className="space-y-2">
            {conflicts.map((conflict, index) => (
              <p key={index} className="text-yellow-300 text-sm">
                {conflict.description}
              </p>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-emerald-900 p-4">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Book Your Service</h1>
          <p className="text-gray-400">Complete your booking in just a few steps</p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            {steps.map((step) => (
              <div
                key={step.id}
                className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  step.id === currentStep
                    ? 'bg-emerald-500 border-emerald-500 text-white'
                    : step.id < currentStep
                    ? 'bg-emerald-500/20 border-emerald-500 text-emerald-400'
                    : 'bg-white/[0.08] border-white/40 text-gray-400'
                }`}
              >
                {step.id < currentStep ? (
                  <CheckCircle className="w-6 h-6" />
                ) : (
                  step.id
                )}
              </div>
            ))}
          </div>
          <div className="w-full bg-white/[0.08] rounded-full h-2">
            <div
              className="bg-emerald-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
            />
          </div>
        </div>

        {/* Form Content */}
        <div className="bg-white/[0.08] backdrop-blur-[30px] border border-white/40 rounded-xl p-8">
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-white mb-2">
              {steps[currentStep - 1].title}
            </h2>
            <p className="text-gray-400">
              {steps[currentStep - 1].description}
            </p>
          </div>

          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderStepContent()}
            </motion.div>
          </AnimatePresence>

          {/* Validation Indicator */}
          {isValidating && (
            <div className="flex items-center gap-2 mt-4 text-emerald-400">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-sm">Validating...</span>
            </div>
          )}

          {/* General Errors */}
          {errors.general && (
            <div className="mt-4 p-4 bg-red-500/20 border border-red-500/40 rounded-xl">
              <p className="text-red-400">{errors.general}</p>
            </div>
          )}

          {/* Warnings */}
          {warnings.length > 0 && (
            <div className="mt-4 p-4 bg-yellow-500/20 border border-yellow-500/40 rounded-xl">
              {warnings.map((warning, index) => (
                <p key={index} className="text-yellow-400 text-sm">
                  {warning.message}
                </p>
              ))}
            </div>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <button
            onClick={currentStep === 1 ? onCancel : goToPreviousStep}
            className="flex items-center gap-2 px-6 py-3 bg-white/[0.08] border border-white/40 rounded-xl text-white hover:bg-white/[0.12] transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            {currentStep === 1 ? 'Cancel' : 'Previous'}
          </button>

          {currentStep < steps.length ? (
            <button
              onClick={goToNextStep}
              disabled={!isCurrentStepValid() || isValidating}
              className="flex items-center gap-2 px-6 py-3 bg-emerald-500 text-white rounded-xl hover:bg-emerald-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
              <ArrowRight className="w-5 h-5" />
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={!isCurrentStepValid() || isSubmitting || conflicts.length > 0}
              className="flex items-center gap-2 px-6 py-3 bg-emerald-500 text-white rounded-xl hover:bg-emerald-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Submitting...
                </>
              ) : (
                <>
                  <Save className="w-5 h-5" />
                  Submit Booking
                </>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
