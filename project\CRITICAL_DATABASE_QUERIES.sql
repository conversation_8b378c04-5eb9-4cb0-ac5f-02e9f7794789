-- CRITICAL DATABASE INVESTIGATION QUERIES
-- Execute these queries to verify construction form data storage

-- ============================================================================
-- QUERY SET 1: BASIC DATA VERIFICATION
-- ============================================================================

-- 1.1 Check if ANY construction bookings exist
SELECT 
    COUNT(*) as total_construction_bookings
FROM booking_forms 
WHERE service_type IN ('construction', 'residential_post_construction');

-- 1.2 Get breakdown by service type
SELECT 
    service_type,
    COUNT(*) as count,
    MIN(created_at) as first_booking,
    MAX(created_at) as latest_booking
FROM booking_forms 
WHERE service_type IN ('construction', 'residential_post_construction')
GROUP BY service_type
ORDER BY count DESC;

-- 1.3 Recent construction bookings (last 30 days)
SELECT 
    id,
    service_type,
    status,
    created_at,
    user_id,
    CASE 
        WHEN contact->>'firstName' IS NOT NULL THEN 'Has Contact'
        ELSE 'Missing Contact'
    END as contact_status,
    CASE 
        WHEN property_details IS NOT NULL THEN 'Has Property Details'
        ELSE 'Missing Property Details'
    END as property_status
FROM booking_forms 
WHERE service_type IN ('construction', 'residential_post_construction')
  AND created_at >= NOW() - INTERVAL '30 days'
ORDER BY created_at DESC;

-- ============================================================================
-- QUERY SET 2: DATA QUALITY ANALYSIS
-- ============================================================================

-- 2.1 Check data completeness for construction bookings
SELECT 
    service_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN property_details IS NULL THEN 1 END) as missing_property_details,
    COUNT(CASE WHEN service_details IS NULL THEN 1 END) as missing_service_details,
    COUNT(CASE WHEN contact IS NULL THEN 1 END) as missing_contact,
    COUNT(CASE WHEN schedule IS NULL THEN 1 END) as missing_schedule,
    COUNT(CASE WHEN user_id IS NULL THEN 1 END) as missing_user_id
FROM booking_forms 
WHERE service_type IN ('construction', 'residential_post_construction')
GROUP BY service_type;

-- 2.2 Sample construction booking details (anonymized)
SELECT 
    id,
    service_type,
    status,
    created_at,
    user_id,
    -- Property details structure
    CASE 
        WHEN property_details->>'propertyType' IS NOT NULL THEN property_details->>'propertyType'
        ELSE 'Not specified'
    END as property_type,
    CASE 
        WHEN property_details->>'propertySize' IS NOT NULL THEN property_details->>'propertySize'
        ELSE 'Not specified'
    END as property_size,
    -- Service details structure
    CASE 
        WHEN service_details->>'cleaningServices' IS NOT NULL THEN 'Has cleaning services'
        ELSE 'No cleaning services'
    END as cleaning_services_status,
    -- Contact structure (anonymized)
    CASE 
        WHEN contact->>'email' IS NOT NULL THEN 'Has email'
        ELSE 'No email'
    END as email_status,
    total_price
FROM booking_forms 
WHERE service_type IN ('construction', 'residential_post_construction')
ORDER BY created_at DESC
LIMIT 5;

-- ============================================================================
-- QUERY SET 3: USER ASSOCIATION VERIFICATION
-- ============================================================================

-- 3.1 Check user_id associations
SELECT 
    service_type,
    COUNT(CASE WHEN user_id IS NOT NULL THEN 1 END) as with_user_id,
    COUNT(CASE WHEN user_id IS NULL THEN 1 END) as without_user_id,
    COUNT(*) as total
FROM booking_forms 
WHERE service_type IN ('construction', 'residential_post_construction')
GROUP BY service_type;

-- 3.2 Check for recent submissions by user
SELECT 
    user_id,
    service_type,
    COUNT(*) as booking_count,
    MAX(created_at) as latest_booking
FROM booking_forms 
WHERE service_type IN ('construction', 'residential_post_construction')
  AND user_id IS NOT NULL
  AND created_at >= NOW() - INTERVAL '7 days'
GROUP BY user_id, service_type
ORDER BY latest_booking DESC;

-- ============================================================================
-- QUERY SET 4: DIAGNOSTIC QUERIES
-- ============================================================================

-- 4.1 All service types currently in use
SELECT 
    service_type,
    COUNT(*) as count
FROM booking_forms 
GROUP BY service_type
ORDER BY count DESC;

-- 4.2 Check for malformed service types that might be construction-related
SELECT DISTINCT 
    service_type
FROM booking_forms 
WHERE LOWER(service_type) LIKE '%construct%'
   OR LOWER(service_type) LIKE '%post%'
   OR service_type IN ('construction', 'residential_post_construction');

-- 4.3 Recent booking patterns (all types for comparison)
SELECT 
    DATE(created_at) as booking_date,
    service_type,
    COUNT(*) as daily_count
FROM booking_forms 
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY DATE(created_at), service_type
ORDER BY booking_date DESC, daily_count DESC;

-- ============================================================================
-- QUERY SET 5: ERROR DETECTION
-- ============================================================================

-- 5.1 Check for bookings with status indicating problems
SELECT 
    service_type,
    status,
    COUNT(*) as count
FROM booking_forms 
WHERE service_type IN ('construction', 'residential_post_construction')
GROUP BY service_type, status
ORDER BY service_type, count DESC;

-- 5.2 Find bookings with empty or malformed JSONB fields
SELECT 
    id,
    service_type,
    created_at,
    CASE 
        WHEN property_details = '{}' THEN 'Empty property_details'
        WHEN property_details IS NULL THEN 'NULL property_details'
        ELSE 'OK'
    END as property_details_status,
    CASE 
        WHEN service_details = '{}' THEN 'Empty service_details'
        WHEN service_details IS NULL THEN 'NULL service_details'
        ELSE 'OK'
    END as service_details_status,
    CASE 
        WHEN contact = '{}' THEN 'Empty contact'
        WHEN contact IS NULL THEN 'NULL contact'
        ELSE 'OK'
    END as contact_status
FROM booking_forms 
WHERE service_type IN ('construction', 'residential_post_construction')
  AND (
    property_details IS NULL OR property_details = '{}' OR
    service_details IS NULL OR service_details = '{}' OR
    contact IS NULL OR contact = '{}'
  )
ORDER BY created_at DESC;

-- ============================================================================
-- CRITICAL EVIDENCE QUERIES
-- ============================================================================

-- EVIDENCE 1: Prove construction data is being stored
SELECT 
    'EVIDENCE: Construction bookings exist in database' as finding,
    COUNT(*) as record_count,
    MIN(created_at) as earliest,
    MAX(created_at) as latest
FROM booking_forms 
WHERE service_type IN ('construction', 'residential_post_construction');

-- EVIDENCE 2: Show data structure is complete
SELECT 
    'EVIDENCE: Construction bookings have complete data structure' as finding,
    service_type,
    COUNT(*) as total_records,
    COUNT(property_details) as has_property_details,
    COUNT(service_details) as has_service_details,
    COUNT(contact) as has_contact,
    COUNT(schedule) as has_schedule
FROM booking_forms 
WHERE service_type IN ('construction', 'residential_post_construction')
GROUP BY service_type;

-- EVIDENCE 3: Show user association works
SELECT 
    'EVIDENCE: Construction bookings properly associate with users' as finding,
    service_type,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(*) as total_bookings
FROM booking_forms 
WHERE service_type IN ('construction', 'residential_post_construction')
  AND user_id IS NOT NULL
GROUP BY service_type;

-- ============================================================================
-- INSTRUCTIONS FOR EXECUTION
-- ============================================================================

/*
TO EXECUTE THESE QUERIES:

1. SUPABASE DASHBOARD METHOD:
   - Go to your Supabase project dashboard
   - Navigate to SQL Editor
   - Run queries one by one or in groups

2. COMMAND LINE METHOD (if psql access available):
   psql "postgresql://[your-connection-string]"
   
3. PROGRAMMATIC METHOD:
   Use the existing Supabase client in the application
   
EXPECTED RESULTS:
- If construction forms are working: Queries will return data
- If there's a storage issue: Queries will return empty results
- If there's a data quality issue: Queries will show incomplete records

CRITICAL SUCCESS INDICATORS:
✅ Query 1.1 returns count > 0
✅ Query 2.1 shows complete data structures
✅ Query 3.1 shows proper user associations
✅ Query 5.1 shows mostly 'pending' or 'confirmed' statuses

FAILURE INDICATORS:
❌ Query 1.1 returns count = 0 (no construction bookings stored)
❌ Query 2.1 shows high numbers of missing fields
❌ Query 3.1 shows many records without user_id
❌ Query 5.1 shows many 'failed' or error statuses
*/