/*
  Enhanced Booking System Migration
  
  This migration enhances the booking system with:
  1. Booking activities tracking
  2. Booking notes system
  3. Improved constraints and indexes
  4. Calendar integration support
  5. Advanced status management
*/

-- ============================================================================
-- 1. Enhance booking_forms table with additional fields
-- ============================================================================

-- Add new columns if they don't exist
ALTER TABLE booking_forms 
ADD COLUMN IF NOT EXISTS priority text DEFAULT 'standard' CHECK (priority IN ('low', 'standard', 'high', 'urgent')),
ADD COLUMN IF NOT EXISTS assigned_team_id uuid,
ADD COLUMN IF NOT EXISTS estimated_duration integer, -- in minutes
ADD COLUMN IF NOT EXISTS actual_start_time timestamptz,
ADD COLUMN IF NOT EXISTS actual_end_time timestamptz,
ADD COLUMN IF NOT EXISTS completion_rating integer CHECK (completion_rating >= 1 AND completion_rating <= 5),
ADD COLUMN IF NOT EXISTS customer_feedback text,
ADD COLUMN IF NOT EXISTS internal_notes text,
ADD COLUMN IF NOT EXISTS recurring_booking_id uuid,
ADD COLUMN IF NOT EXISTS cancellation_reason text,
ADD COLUMN IF NOT EXISTS reschedule_count integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_status_change timestamptz DEFAULT now();

-- ============================================================================
-- 2. Create booking_activities table for activity tracking
-- ============================================================================

CREATE TABLE IF NOT EXISTS booking_activities (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id uuid REFERENCES booking_forms(id) ON DELETE CASCADE,
  activity_type text NOT NULL CHECK (
    activity_type IN (
      'created', 'updated', 'status_changed', 'assigned', 'rescheduled',
      'cancelled', 'completed', 'payment_received', 'note_added',
      'customer_contacted', 'team_dispatched', 'service_started',
      'service_completed', 'feedback_received'
    )
  ),
  description text NOT NULL,
  performed_by uuid REFERENCES auth.users(id),
  performed_at timestamptz DEFAULT now(),
  metadata jsonb DEFAULT '{}'::jsonb,
  
  -- Indexes for performance
  created_at timestamptz DEFAULT now()
);

-- Create indexes for booking_activities
CREATE INDEX IF NOT EXISTS idx_booking_activities_booking_id ON booking_activities(booking_id);
CREATE INDEX IF NOT EXISTS idx_booking_activities_type ON booking_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_booking_activities_performed_at ON booking_activities(performed_at);

-- ============================================================================
-- 3. Create booking_notes table for internal and customer notes
-- ============================================================================

CREATE TABLE IF NOT EXISTS booking_notes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id uuid REFERENCES booking_forms(id) ON DELETE CASCADE,
  note_type text DEFAULT 'internal' CHECK (note_type IN ('internal', 'customer', 'system')),
  content text NOT NULL,
  created_by uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  is_private boolean DEFAULT true,
  attachments jsonb DEFAULT '[]'::jsonb,
  
  -- Ensure content is not empty
  CONSTRAINT note_content_not_empty CHECK (length(trim(content)) > 0)
);

-- Create indexes for booking_notes
CREATE INDEX IF NOT EXISTS idx_booking_notes_booking_id ON booking_notes(booking_id);
CREATE INDEX IF NOT EXISTS idx_booking_notes_type ON booking_notes(note_type);
CREATE INDEX IF NOT EXISTS idx_booking_notes_created_at ON booking_notes(created_at);

-- ============================================================================
-- 4. Create teams table for assignment management
-- ============================================================================

CREATE TABLE IF NOT EXISTS teams (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  is_active boolean DEFAULT true,
  specializations text[] DEFAULT '{}',
  max_concurrent_bookings integer DEFAULT 3,
  operating_hours jsonb DEFAULT '{}'::jsonb,
  contact_info jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  CONSTRAINT team_name_not_empty CHECK (length(trim(name)) > 0)
);

-- Create indexes for teams
CREATE INDEX IF NOT EXISTS idx_teams_active ON teams(is_active);
CREATE INDEX IF NOT EXISTS idx_teams_specializations ON teams USING GIN(specializations);

-- ============================================================================
-- 5. Create team_members table for team composition
-- ============================================================================

CREATE TABLE IF NOT EXISTS team_members (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  team_id uuid REFERENCES teams(id) ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  role text DEFAULT 'member' CHECK (role IN ('leader', 'member', 'trainee')),
  is_active boolean DEFAULT true,
  joined_at timestamptz DEFAULT now(),
  
  -- Prevent duplicate team memberships
  UNIQUE(team_id, user_id)
);

-- Create indexes for team_members
CREATE INDEX IF NOT EXISTS idx_team_members_team_id ON team_members(team_id);
CREATE INDEX IF NOT EXISTS idx_team_members_user_id ON team_members(user_id);
CREATE INDEX IF NOT EXISTS idx_team_members_active ON team_members(is_active);

-- ============================================================================
-- 6. Create booking_conflicts table for conflict management
-- ============================================================================

CREATE TABLE IF NOT EXISTS booking_conflicts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id_1 uuid REFERENCES booking_forms(id) ON DELETE CASCADE,
  booking_id_2 uuid REFERENCES booking_forms(id) ON DELETE CASCADE,
  conflict_type text NOT NULL CHECK (
    conflict_type IN (
      'time_overlap', 'resource_conflict', 'team_availability',
      'location_proximity', 'service_incompatibility'
    )
  ),
  severity text DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  description text NOT NULL,
  resolution_status text DEFAULT 'unresolved' CHECK (
    resolution_status IN ('unresolved', 'auto_resolved', 'manually_resolved', 'ignored')
  ),
  resolution_method text,
  resolved_by uuid REFERENCES auth.users(id),
  resolved_at timestamptz,
  created_at timestamptz DEFAULT now(),
  
  -- Ensure different bookings
  CONSTRAINT different_bookings CHECK (booking_id_1 != booking_id_2)
);

-- Create indexes for booking_conflicts
CREATE INDEX IF NOT EXISTS idx_booking_conflicts_booking_1 ON booking_conflicts(booking_id_1);
CREATE INDEX IF NOT EXISTS idx_booking_conflicts_booking_2 ON booking_conflicts(booking_id_2);
CREATE INDEX IF NOT EXISTS idx_booking_conflicts_status ON booking_conflicts(resolution_status);
CREATE INDEX IF NOT EXISTS idx_booking_conflicts_severity ON booking_conflicts(severity);

-- ============================================================================
-- 7. Create calendar_events table for calendar integration
-- ============================================================================

CREATE TABLE IF NOT EXISTS calendar_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id uuid REFERENCES booking_forms(id) ON DELETE CASCADE,
  external_calendar_id text,
  external_event_id text,
  calendar_provider text CHECK (calendar_provider IN ('google', 'outlook', 'apple', 'custom')),
  event_title text NOT NULL,
  event_description text,
  start_time timestamptz NOT NULL,
  end_time timestamptz NOT NULL,
  location text,
  attendees jsonb DEFAULT '[]'::jsonb,
  reminders jsonb DEFAULT '[]'::jsonb,
  sync_status text DEFAULT 'synced' CHECK (sync_status IN ('synced', 'pending', 'failed', 'deleted')),
  last_synced_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now(),
  
  -- Ensure valid time range
  CONSTRAINT valid_time_range CHECK (end_time > start_time)
);

-- Create indexes for calendar_events
CREATE INDEX IF NOT EXISTS idx_calendar_events_booking_id ON calendar_events(booking_id);
CREATE INDEX IF NOT EXISTS idx_calendar_events_provider ON calendar_events(calendar_provider);
CREATE INDEX IF NOT EXISTS idx_calendar_events_sync_status ON calendar_events(sync_status);
CREATE INDEX IF NOT EXISTS idx_calendar_events_start_time ON calendar_events(start_time);

-- ============================================================================
-- 8. Add triggers for automatic activity logging
-- ============================================================================

-- Function to log booking activities automatically
CREATE OR REPLACE FUNCTION log_booking_activity()
RETURNS TRIGGER AS $$
BEGIN
  -- Log different types of changes
  IF TG_OP = 'INSERT' THEN
    INSERT INTO booking_activities (booking_id, activity_type, description, performed_by)
    VALUES (NEW.id, 'created', 'Booking created', NEW.user_id);
    RETURN NEW;
  END IF;
  
  IF TG_OP = 'UPDATE' THEN
    -- Log status changes
    IF OLD.status != NEW.status THEN
      INSERT INTO booking_activities (booking_id, activity_type, description, performed_by, metadata)
      VALUES (
        NEW.id, 
        'status_changed', 
        'Status changed from ' || OLD.status || ' to ' || NEW.status,
        NEW.user_id,
        jsonb_build_object('old_status', OLD.status, 'new_status', NEW.status)
      );
      
      -- Update last status change timestamp
      NEW.last_status_change = now();
    END IF;
    
    -- Log assignment changes
    IF COALESCE(OLD.assigned_team_id::text, '') != COALESCE(NEW.assigned_team_id::text, '') THEN
      INSERT INTO booking_activities (booking_id, activity_type, description, performed_by, metadata)
      VALUES (
        NEW.id,
        'assigned',
        'Team assignment updated',
        NEW.user_id,
        jsonb_build_object('old_team', OLD.assigned_team_id, 'new_team', NEW.assigned_team_id)
      );
    END IF;
    
    -- Log reschedule count increases
    IF OLD.reschedule_count != NEW.reschedule_count THEN
      INSERT INTO booking_activities (booking_id, activity_type, description, performed_by)
      VALUES (NEW.id, 'rescheduled', 'Booking rescheduled', NEW.user_id);
    END IF;
    
    RETURN NEW;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for booking activity logging
DROP TRIGGER IF EXISTS booking_activity_trigger ON booking_forms;
CREATE TRIGGER booking_activity_trigger
  AFTER INSERT OR UPDATE ON booking_forms
  FOR EACH ROW
  EXECUTE FUNCTION log_booking_activity();

-- ============================================================================
-- 9. Enhanced RLS Policies
-- ============================================================================

-- Enable RLS on all new tables
ALTER TABLE booking_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE booking_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE booking_conflicts ENABLE ROW LEVEL SECURITY;
ALTER TABLE calendar_events ENABLE ROW LEVEL SECURITY;

-- RLS Policies for booking_activities
CREATE POLICY "Users can view activities for their bookings"
  ON booking_activities FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM booking_forms 
      WHERE booking_forms.id = booking_activities.booking_id 
      AND booking_forms.user_id = auth.uid()
    )
  );

CREATE POLICY "System can insert activities"
  ON booking_activities FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- RLS Policies for booking_notes
CREATE POLICY "Users can view notes for their bookings"
  ON booking_notes FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM booking_forms 
      WHERE booking_forms.id = booking_notes.booking_id 
      AND booking_forms.user_id = auth.uid()
    )
    AND (note_type = 'customer' OR is_private = false)
  );

CREATE POLICY "Users can create notes for their bookings"
  ON booking_notes FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM booking_forms 
      WHERE booking_forms.id = booking_notes.booking_id 
      AND booking_forms.user_id = auth.uid()
    )
  );

-- RLS Policies for teams (readable by all authenticated users)
CREATE POLICY "Authenticated users can view teams"
  ON teams FOR SELECT
  TO authenticated
  USING (is_active = true);

-- RLS Policies for calendar_events
CREATE POLICY "Users can view calendar events for their bookings"
  ON calendar_events FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM booking_forms 
      WHERE booking_forms.id = calendar_events.booking_id 
      AND booking_forms.user_id = auth.uid()
    )
  );

-- ============================================================================
-- 10. Create helpful views for common queries
-- ============================================================================

-- View for booking overview with team and activity info
CREATE OR REPLACE VIEW booking_overview AS
SELECT 
  bf.*,
  t.name as assigned_team_name,
  t.specializations as team_specializations,
  (
    SELECT COUNT(*) 
    FROM booking_activities ba 
    WHERE ba.booking_id = bf.id
  ) as activity_count,
  (
    SELECT COUNT(*) 
    FROM booking_notes bn 
    WHERE bn.booking_id = bf.id
  ) as notes_count,
  (
    SELECT COUNT(*) 
    FROM booking_conflicts bc 
    WHERE (bc.booking_id_1 = bf.id OR bc.booking_id_2 = bf.id)
    AND bc.resolution_status = 'unresolved'
  ) as unresolved_conflicts
FROM booking_forms bf
LEFT JOIN teams t ON bf.assigned_team_id = t.id;

-- View for recent booking activities
CREATE OR REPLACE VIEW recent_booking_activities AS
SELECT 
  ba.*,
  bf.service_type,
  bf.status as booking_status,
  CONCAT(
    COALESCE(bf.contact->>'firstName', ''), 
    ' ', 
    COALESCE(bf.contact->>'lastName', '')
  ) as customer_name
FROM booking_activities ba
JOIN booking_forms bf ON ba.booking_id = bf.id
ORDER BY ba.performed_at DESC
LIMIT 50;

-- ============================================================================
-- 11. Create useful functions for booking management
-- ============================================================================

-- Function to get booking statistics
CREATE OR REPLACE FUNCTION get_booking_statistics(
  p_user_id uuid DEFAULT NULL,
  p_start_date timestamptz DEFAULT NULL,
  p_end_date timestamptz DEFAULT NULL
)
RETURNS jsonb AS $$
DECLARE
  result jsonb;
  booking_count integer;
  total_revenue numeric;
BEGIN
  -- Build the query conditions
  WITH filtered_bookings AS (
    SELECT * FROM booking_forms
    WHERE (p_user_id IS NULL OR user_id = p_user_id)
    AND (p_start_date IS NULL OR created_at >= p_start_date)
    AND (p_end_date IS NULL OR created_at <= p_end_date)
  )
  SELECT 
    jsonb_build_object(
      'totalBookings', COUNT(*),
      'totalRevenue', COALESCE(SUM(total_price), 0),
      'averageBookingValue', COALESCE(AVG(total_price), 0),
      'bookingsByStatus', (
        SELECT jsonb_object_agg(status, count)
        FROM (
          SELECT status, COUNT(*) as count
          FROM filtered_bookings
          GROUP BY status
        ) status_counts
      ),
      'bookingsByService', (
        SELECT jsonb_object_agg(service_type, count)
        FROM (
          SELECT service_type, COUNT(*) as count
          FROM filtered_bookings
          GROUP BY service_type
        ) service_counts
      )
    )
  INTO result
  FROM filtered_bookings;
  
  RETURN COALESCE(result, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql;

-- Function to check for booking conflicts
CREATE OR REPLACE FUNCTION check_booking_conflicts(
  p_booking_id uuid,
  p_date date,
  p_time_slot text,
  p_service_type text,
  p_address text
)
RETURNS jsonb AS $$
DECLARE
  conflicts jsonb[];
  conflict_record jsonb;
BEGIN
  -- Check for time-based conflicts
  FOR conflict_record IN
    SELECT jsonb_build_object(
      'type', 'time_overlap',
      'conflicting_booking_id', id,
      'description', 'Time slot already booked for ' || service_type
    )
    FROM booking_forms
    WHERE id != p_booking_id
    AND status IN ('confirmed', 'in_progress')
    AND schedule->>'preferredDate' = p_date::text
    AND schedule->>'preferredTime' = p_time_slot
  LOOP
    conflicts := conflicts || conflict_record;
  END LOOP;
  
  -- Check for address-based conflicts (same day, same address)
  FOR conflict_record IN
    SELECT jsonb_build_object(
      'type', 'location_conflict',
      'conflicting_booking_id', id,
      'description', 'Another booking exists at this address on the same day'
    )
    FROM booking_forms
    WHERE id != p_booking_id
    AND status IN ('confirmed', 'in_progress')
    AND schedule->>'preferredDate' = p_date::text
    AND property_details->>'address' ILIKE '%' || p_address || '%'
  LOOP
    conflicts := conflicts || conflict_record;
  END LOOP;
  
  RETURN jsonb_build_object('conflicts', COALESCE(conflicts, '{}'::jsonb[]));
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- 12. Add helpful comments
-- ============================================================================

COMMENT ON TABLE booking_activities IS 'Tracks all activities and changes related to bookings';
COMMENT ON TABLE booking_notes IS 'Stores internal and customer-facing notes for bookings';
COMMENT ON TABLE teams IS 'Manages cleaning teams and their specializations';
COMMENT ON TABLE team_members IS 'Tracks team composition and member roles';
COMMENT ON TABLE booking_conflicts IS 'Identifies and tracks conflicts between bookings';
COMMENT ON TABLE calendar_events IS 'Manages calendar integration for booking scheduling';

COMMENT ON FUNCTION get_booking_statistics IS 'Returns comprehensive booking statistics for analytics';
COMMENT ON FUNCTION check_booking_conflicts IS 'Checks for potential conflicts when scheduling bookings';
