/**
 * Enhanced Booking Validation Service
 * 
 * Provides comprehensive validation for booking data including:
 * - Business rule validation
 * - Date/time validation
 * - Conflict detection
 * - Data sanitization
 */

import { addDays, isAfter, isBefore, isToday, parseISO, isValid } from 'date-fns';
import { StandardizedBookingData } from '../api/bookingService';
import { supabase } from '../supabase/client';

export interface ValidationRule {
  field: string;
  type: 'required' | 'email' | 'phone' | 'date' | 'time' | 'custom';
  message: string;
  validator?: (value: any, formData: any) => boolean;
}

export interface BusinessRule {
  name: string;
  description: string;
  validator: (bookingData: StandardizedBookingData) => Promise<{ isValid: boolean; message?: string }>;
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  severity: 'error' | 'warning';
}

export interface ConflictCheck {
  type: 'time_overlap' | 'double_booking' | 'resource_conflict';
  description: string;
  conflictingBookingId?: string;
}

export class BookingValidationService {
  
  /**
   * Comprehensive booking validation
   */
  static async validateBooking(
    bookingData: Partial<StandardizedBookingData>,
    serviceType: string
  ): Promise<{
    isValid: boolean;
    errors: ValidationError[];
    warnings: ValidationError[];
    conflicts: ConflictCheck[];
  }> {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];
    const conflicts: ConflictCheck[] = [];

    try {
      // 1. Basic field validation
      const fieldValidation = this.validateRequiredFields(bookingData, serviceType);
      errors.push(...fieldValidation);

      // 2. Data format validation
      const formatValidation = this.validateDataFormats(bookingData);
      errors.push(...formatValidation);

      // 3. Business rule validation
      if (errors.length === 0) {
        const businessValidation = await this.validateBusinessRules(bookingData as StandardizedBookingData);
        errors.push(...businessValidation.errors);
        warnings.push(...businessValidation.warnings);
      }

      // 4. Conflict detection
      if (errors.length === 0) {
        const conflictCheck = await this.checkBookingConflicts(bookingData as StandardizedBookingData);
        conflicts.push(...conflictCheck);
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        conflicts
      };

    } catch (error) {
      console.error('Booking validation failed:', error);
      return {
        isValid: false,
        errors: [{
          field: 'general',
          message: 'Validation system error. Please try again.',
          code: 'SYSTEM_ERROR',
          severity: 'error'
        }],
        warnings: [],
        conflicts: []
      };
    }
  }

  /**
   * Validate required fields based on service type
   */
  private static validateRequiredFields(
    bookingData: Partial<StandardizedBookingData>,
    serviceType: string
  ): ValidationError[] {
    const errors: ValidationError[] = [];
    const rules = this.getServiceValidationRules(serviceType);

    for (const rule of rules) {
      const value = this.getNestedValue(bookingData, rule.field);
      
      if (rule.type === 'required' && (!value || value.toString().trim() === '')) {
        errors.push({
          field: rule.field,
          message: rule.message,
          code: 'REQUIRED_FIELD',
          severity: 'error'
        });
      }
    }

    return errors;
  }

  /**
   * Validate data formats (email, phone, dates, etc.)
   */
  private static validateDataFormats(bookingData: Partial<StandardizedBookingData>): ValidationError[] {
    const errors: ValidationError[] = [];

    // Email validation
    const email = bookingData.contact?.email;
    if (email && !this.isValidEmail(email)) {
      errors.push({
        field: 'contact.email',
        message: 'Please enter a valid email address',
        code: 'INVALID_EMAIL',
        severity: 'error'
      });
    }

    // Phone validation
    const phone = bookingData.contact?.phone;
    if (phone && !this.isValidPhone(phone)) {
      errors.push({
        field: 'contact.phone',
        message: 'Please enter a valid phone number',
        code: 'INVALID_PHONE',
        severity: 'error'
      });
    }

    // Date validation
    const preferredDate = bookingData.schedule?.preferredDate;
    if (preferredDate) {
      const dateValidation = this.validateDate(preferredDate);
      if (!dateValidation.isValid) {
        errors.push({
          field: 'schedule.preferredDate',
          message: dateValidation.message || 'Invalid date',
          code: 'INVALID_DATE',
          severity: 'error'
        });
      }
    }

    return errors;
  }

  /**
   * Validate business rules
   */
  private static async validateBusinessRules(
    bookingData: StandardizedBookingData
  ): Promise<{ errors: ValidationError[]; warnings: ValidationError[] }> {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    const rules = this.getBusinessRules();

    for (const rule of rules) {
      try {
        const result = await rule.validator(bookingData);
        if (!result.isValid) {
          errors.push({
            field: 'business_rule',
            message: result.message || `Business rule violation: ${rule.name}`,
            code: `BUSINESS_RULE_${rule.name.toUpperCase().replace(/\s+/g, '_')}`,
            severity: 'error'
          });
        }
      } catch (error) {
        console.warn(`Business rule ${rule.name} validation failed:`, error);
      }
    }

    return { errors, warnings };
  }

  /**
   * Check for booking conflicts
   */
  private static async checkBookingConflicts(
    bookingData: StandardizedBookingData
  ): Promise<ConflictCheck[]> {
    const conflicts: ConflictCheck[] = [];

    try {
      // Check for time overlaps
      const timeConflicts = await this.checkTimeConflicts(bookingData);
      conflicts.push(...timeConflicts);

      // Check for resource conflicts
      const resourceConflicts = await this.checkResourceConflicts(bookingData);
      conflicts.push(...resourceConflicts);

    } catch (error) {
      console.error('Conflict checking failed:', error);
    }

    return conflicts;
  }

  /**
   * Check for time-based conflicts
   */
  private static async checkTimeConflicts(
    bookingData: StandardizedBookingData
  ): Promise<ConflictCheck[]> {
    const conflicts: ConflictCheck[] = [];

    if (!bookingData.schedule?.preferredDate || !supabase) {
      return conflicts;
    }

    try {
      const { data: existingBookings, error } = await supabase
        .from('booking_forms')
        .select('id, schedule, service_type, property_details')
        .eq('status', 'confirmed')
        .contains('schedule', { preferredDate: bookingData.schedule.preferredDate });

      if (error) {
        console.error('Error checking time conflicts:', error);
        return conflicts;
      }

      // Check for overlapping time slots
      for (const existing of existingBookings || []) {
        if (this.hasTimeOverlap(bookingData.schedule, existing.schedule)) {
          conflicts.push({
            type: 'time_overlap',
            description: `Time slot overlaps with existing ${existing.service_type} booking`,
            conflictingBookingId: existing.id
          });
        }
      }

    } catch (error) {
      console.error('Time conflict check failed:', error);
    }

    return conflicts;
  }

  /**
   * Check for resource conflicts
   */
  private static async checkResourceConflicts(
    bookingData: StandardizedBookingData
  ): Promise<ConflictCheck[]> {
    const conflicts: ConflictCheck[] = [];

    // Check for double booking at same address
    if (!bookingData.property_details?.address || !supabase) {
      return conflicts;
    }

    try {
      const { data: existingBookings, error } = await supabase
        .from('booking_forms')
        .select('id, property_details, schedule, service_type')
        .eq('status', 'confirmed')
        .contains('property_details', { address: bookingData.property_details.address });

      if (error) {
        console.error('Error checking resource conflicts:', error);
        return conflicts;
      }

      // Check for same-day bookings at same address
      for (const existing of existingBookings || []) {
        if (existing.schedule?.preferredDate === bookingData.schedule?.preferredDate) {
          conflicts.push({
            type: 'double_booking',
            description: `Another ${existing.service_type} booking exists for this address on the same day`,
            conflictingBookingId: existing.id
          });
        }
      }

    } catch (error) {
      console.error('Resource conflict check failed:', error);
    }

    return conflicts;
  }

  /**
   * Get validation rules for service type
   */
  private static getServiceValidationRules(serviceType: string): ValidationRule[] {
    const baseRules: ValidationRule[] = [
      {
        field: 'contact.firstName',
        type: 'required',
        message: 'First name is required'
      },
      {
        field: 'contact.lastName',
        type: 'required',
        message: 'Last name is required'
      },
      {
        field: 'contact.email',
        type: 'required',
        message: 'Email address is required'
      },
      {
        field: 'contact.phone',
        type: 'required',
        message: 'Phone number is required'
      },
      {
        field: 'property_details.address',
        type: 'required',
        message: 'Property address is required'
      },
      {
        field: 'schedule.preferredDate',
        type: 'required',
        message: 'Preferred date is required'
      }
    ];

    // Add service-specific rules
    const serviceSpecificRules: Record<string, ValidationRule[]> = {
      'office': [
        {
          field: 'contact.companyName',
          type: 'required',
          message: 'Company name is required for office cleaning'
        }
      ],
      'residential_post_construction': [
        {
          field: 'service_details.constructionType',
          type: 'required',
          message: 'Construction type is required'
        }
      ]
    };

    return [...baseRules, ...(serviceSpecificRules[serviceType] || [])];
  }

  /**
   * Get business rules
   */
  private static getBusinessRules(): BusinessRule[] {
    return [
      {
        name: 'Future Date Required',
        description: 'Booking date must be in the future',
        validator: async (booking) => {
          const preferredDate = booking.schedule?.preferredDate;
          if (!preferredDate) return { isValid: true };

          const bookingDate = parseISO(preferredDate);
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          return {
            isValid: isAfter(bookingDate, today) || isToday(bookingDate),
            message: 'Booking date must be today or in the future'
          };
        }
      },
      {
        name: 'Advance Booking Limit',
        description: 'Bookings cannot be made more than 6 months in advance',
        validator: async (booking) => {
          const preferredDate = booking.schedule?.preferredDate;
          if (!preferredDate) return { isValid: true };

          const bookingDate = parseISO(preferredDate);
          const maxDate = addDays(new Date(), 180); // 6 months

          return {
            isValid: isBefore(bookingDate, maxDate),
            message: 'Bookings cannot be scheduled more than 6 months in advance'
          };
        }
      },
      {
        name: 'Service Area Validation',
        description: 'Service must be available in the requested area',
        validator: async (booking) => {
          const zipCode = booking.property_details?.zipCode;
          if (!zipCode) return { isValid: true };

          // Define service areas (this could be moved to database)
          const serviceAreas = ['12345', '23456', '34567', '45678', '56789'];
          
          return {
            isValid: serviceAreas.includes(zipCode),
            message: 'Service is not available in your area'
          };
        }
      }
    ];
  }

  /**
   * Utility methods
   */
  private static getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private static isValidPhone(phone: string): boolean {
    const phoneRegex = /^[\+]?[1-9]?[\d\s\-\(\)]{10,14}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  }

  private static validateDate(dateString: string): { isValid: boolean; message?: string } {
    const date = parseISO(dateString);
    
    if (!isValid(date)) {
      return { isValid: false, message: 'Invalid date format' };
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (isBefore(date, today)) {
      return { isValid: false, message: 'Date cannot be in the past' };
    }

    return { isValid: true };
  }

  private static hasTimeOverlap(schedule1: any, schedule2: any): boolean {
    // Simplified time overlap check
    // In a real implementation, this would compare actual time slots
    if (!schedule1?.preferredTime || !schedule2?.preferredTime) {
      return false;
    }

    return schedule1.preferredTime === schedule2.preferredTime;
  }
}
