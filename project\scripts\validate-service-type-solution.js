#!/usr/bin/env node

/**
 * SERVICE TYPE SOLUTION VALIDATION SCRIPT
 * 
 * MISSION: Validate that the unified service type solution works correctly
 * 
 * This script tests:
 * 1. Service type registry consistency
 * 2. Normalization function accuracy  
 * 3. Database constraint compatibility
 * 4. Form integration readiness
 * 
 * BACKEND ARCHITECT SOLUTION - Phase 3: Validation
 */

import { ServiceTypeConfig, normalizeServiceType, isValidServiceType } from '../src/lib/services/serviceTypeRegistry.js';
import { ServiceTypeStandardizer } from '../src/lib/services/serviceTypeStandardizer.js';

/**
 * Test Cases for Service Type Normalization
 */
const TEST_CASES = [
  // Commercial Post-Construction Cases
  {
    input: 'construction',
    expected: 'construction',
    category: 'commercial',
    description: 'Direct commercial post-construction match'
  },
  {
    input: 'post-construction',
    expected: 'residential_post_construction', // Default to residential when context unclear
    category: 'residential',
    description: 'Generic post-construction should default to residential'
  },
  {
    input: 'commercial post-construction',
    expected: 'construction',
    category: 'commercial', 
    description: 'Commercial context post-construction'
  },
  {
    input: 'office construction cleanup',
    expected: 'construction',
    category: 'commercial',
    description: 'Office context construction cleanup'
  },
  {
    input: 'business post-construction',
    expected: 'construction',
    category: 'commercial',
    description: 'Business context post-construction'
  },
  
  // Residential Post-Construction Cases
  {
    input: 'residential_post_construction',
    expected: 'residential_post_construction',
    category: 'residential',
    description: 'Direct residential post-construction match'
  },
  {
    input: 'home post-construction',
    expected: 'residential_post_construction',
    category: 'residential',
    description: 'Home context post-construction'
  },
  {
    input: 'residential renovation cleaning',
    expected: 'residential_post_construction',
    category: 'residential',
    description: 'Residential renovation cleaning'
  },
  {
    input: 'house construction cleanup',
    expected: 'residential_post_construction',
    category: 'residential',
    description: 'House construction cleanup'
  },
  
  // Other Service Types  
  {
    input: 'office',
    expected: 'office',
    category: 'commercial',
    description: 'Standard office cleaning'
  },
  {
    input: 'residential',
    expected: 'residential',
    category: 'residential',
    description: 'Standard residential cleaning'
  },
  {
    input: 'industrial',
    expected: 'industrial',
    category: 'commercial',
    description: 'Industrial cleaning'
  },
  
  // Edge Cases
  {
    input: '',
    expected: 'residential',
    category: 'residential',
    description: 'Empty string fallback'
  },
  {
    input: null,
    expected: 'residential', 
    category: 'residential',
    description: 'Null input fallback'
  },
  {
    input: 'invalid-service-type',
    expected: 'residential',
    category: 'residential',
    description: 'Invalid service type fallback'
  }
];

/**
 * Form Integration Test Cases
 */
const FORM_TEST_CASES = [
  {
    formType: 'Commercial Post-Construction',
    formData: {
      serviceType: 'construction',
      companyName: 'ABC Construction',
      projectType: 'office-renovation'
    },
    expectedDbValue: 'construction',
    expectedCategory: 'commercial'
  },
  {
    formType: 'Residential Post-Construction', 
    formData: {
      serviceType: 'residential_post_construction',
      firstName: 'John',
      lastName: 'Doe',
      propertyType: 'house'
    },
    expectedDbValue: 'residential_post_construction',
    expectedCategory: 'residential'
  }
];

/**
 * Run normalization tests
 */
function runNormalizationTests() {
  console.log('🧪 RUNNING NORMALIZATION TESTS');
  console.log('================================\n');
  
  let passed = 0;
  let failed = 0;
  
  TEST_CASES.forEach((testCase, index) => {
    try {
      const result = normalizeServiceType(testCase.input);
      const isValid = isValidServiceType(result);
      const category = ServiceTypeConfig.isCommercial(result) ? 'commercial' : 'residential';
      
      const success = result === testCase.expected && 
                     isValid && 
                     category === testCase.category;
      
      if (success) {
        console.log(`✅ Test ${index + 1}: ${testCase.description}`);\n        console.log(`   Input: "${testCase.input}" -> Output: "${result}"`);\n        passed++;\n      } else {\n        console.log(`❌ Test ${index + 1}: ${testCase.description}`);\n        console.log(`   Input: "${testCase.input}"`);\n        console.log(`   Expected: "${testCase.expected}" (${testCase.category})`);\n        console.log(`   Got: "${result}" (${category}, valid: ${isValid})`);\n        failed++;\n      }\n      \n    } catch (error) {\n      console.log(`💥 Test ${index + 1}: ${testCase.description} - ERROR`);\n      console.log(`   Input: "${testCase.input}"`);\n      console.log(`   Error: ${error.message}`);\n      failed++;\n    }\n    \n    console.log('');\n  });\n  \n  return { passed, failed };\n}\n\n/**\n * Test service type standardizer\n */\nfunction runStandardizerTests() {\n  console.log('🔧 RUNNING STANDARDIZER TESTS');\n  console.log('==============================\n');\n  \n  let passed = 0;\n  let failed = 0;\n  \n  FORM_TEST_CASES.forEach((testCase, index) => {\n    try {\n      // Test form data standardization\n      const standardizedData = ServiceTypeStandardizer.standardizeFormServiceType(testCase.formData);\n      \n      // Test database preparation\n      const dbValue = ServiceTypeStandardizer.prepareServiceTypeForDb(testCase.formData.serviceType);\n      \n      // Test display preparation\n      const displayName = ServiceTypeStandardizer.prepareServiceTypeForDisplay(testCase.formData.serviceType);\n      \n      const success = dbValue === testCase.expectedDbValue;\n      \n      if (success) {\n        console.log(`✅ ${testCase.formType} Test`);\n        console.log(`   Form Data: ${JSON.stringify(testCase.formData)}`);\n        console.log(`   DB Value: "${dbValue}"`);\n        console.log(`   Display Name: "${displayName}"`);\n        console.log(`   Standardized: ${JSON.stringify(standardizedData)}`);\n        passed++;\n      } else {\n        console.log(`❌ ${testCase.formType} Test`);\n        console.log(`   Expected DB Value: "${testCase.expectedDbValue}"`);\n        console.log(`   Got DB Value: "${dbValue}"`);\n        failed++;\n      }\n      \n    } catch (error) {\n      console.log(`💥 ${testCase.formType} Test - ERROR`);\n      console.log(`   Error: ${error.message}`);\n      failed++;\n    }\n    \n    console.log('');\n  });\n  \n  return { passed, failed };\n}\n\n/**\n * Test registry consistency\n */\nfunction runRegistryTests() {\n  console.log('📋 RUNNING REGISTRY CONSISTENCY TESTS');\n  console.log('=====================================\n');\n  \n  let passed = 0;\n  let failed = 0;\n  \n  // Test that both post-construction types are defined\n  const constructionService = ServiceTypeConfig.types['construction'];\n  const residentialConstructionService = ServiceTypeConfig.types['residential_post_construction'];\n  \n  if (constructionService && constructionService.category === 'commercial') {\n    console.log(`✅ Commercial post-construction service defined correctly`);\n    console.log(`   Name: ${constructionService.name}`);\n    console.log(`   DB Value: ${constructionService.dbValue}`);\n    console.log(`   Category: ${constructionService.category}`);\n    passed++;\n  } else {\n    console.log(`❌ Commercial post-construction service not properly defined`);\n    failed++;\n  }\n  \n  if (residentialConstructionService && residentialConstructionService.category === 'residential') {\n    console.log(`\\n✅ Residential post-construction service defined correctly`);\n    console.log(`   Name: ${residentialConstructionService.name}`);\n    console.log(`   DB Value: ${residentialConstructionService.dbValue}`);\n    console.log(`   Category: ${residentialConstructionService.category}`);\n    passed++;\n  } else {\n    console.log(`\\n❌ Residential post-construction service not properly defined`);\n    failed++;\n  }\n  \n  // Test SQL constraint generation\n  try {\n    const sqlConstraint = ServiceTypeConfig.getSqlConstraint();\n    if (sqlConstraint.includes(\"'construction'\") && sqlConstraint.includes(\"'residential_post_construction'\")) {\n      console.log(`\\n✅ SQL constraint includes both post-construction types`);\n      passed++;\n    } else {\n      console.log(`\\n❌ SQL constraint missing post-construction types`);\n      console.log(`   Generated: ${sqlConstraint}`);\n      failed++;\n    }\n  } catch (error) {\n    console.log(`\\n💥 SQL constraint generation failed: ${error.message}`);\n    failed++;\n  }\n  \n  return { passed, failed };\n}\n\n/**\n * Generate validation report\n */\nfunction generateValidationReport(results) {\n  console.log(`\\n📊 VALIDATION REPORT`);\n  console.log(`====================`);\n  console.log(`Generated: ${new Date().toISOString()}\\n`);\n  \n  let totalPassed = 0;\n  let totalFailed = 0;\n  \n  for (const [testType, result] of Object.entries(results)) {\n    console.log(`${testType}:`);\n    console.log(`  ✅ Passed: ${result.passed}`);\n    console.log(`  ❌ Failed: ${result.failed}`);\n    console.log(`  📈 Success Rate: ${((result.passed / (result.passed + result.failed)) * 100).toFixed(1)}%\\n`);\n    \n    totalPassed += result.passed;\n    totalFailed += result.failed;\n  }\n  \n  console.log(`📈 OVERALL RESULTS`);\n  console.log(`  Total Tests: ${totalPassed + totalFailed}`);\n  console.log(`  ✅ Passed: ${totalPassed}`);\n  console.log(`  ❌ Failed: ${totalFailed}`);\n  console.log(`  📈 Success Rate: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);\n  \n  if (totalFailed === 0) {\n    console.log(`\\n🎉 ALL TESTS PASSED! Service type solution is ready for deployment.`);\n  } else {\n    console.log(`\\n⚠️  Some tests failed. Please review the issues above before deployment.`);\n  }\n  \n  return totalFailed === 0;\n}\n\n/**\n * Main validation function\n */\nasync function runValidation() {\n  console.log('🚀 SERVICE TYPE SOLUTION VALIDATION');\n  console.log('====================================\\n');\n  \n  const results = {\n    'Normalization Tests': runNormalizationTests(),\n    'Standardizer Tests': runStandardizerTests(), \n    'Registry Tests': runRegistryTests()\n  };\n  \n  const allTestsPassed = generateValidationReport(results);\n  \n  process.exit(allTestsPassed ? 0 : 1);\n}\n\n// Run validation\nrunValidation().catch(console.error);