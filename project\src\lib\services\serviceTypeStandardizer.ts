/**
 * Service Type Standardization Utility
 * 
 * This utility provides functions to standardize service types across all forms,
 * API calls, and database operations. It ensures consistency and prevents
 * service type mismatches that cause booking failures.
 * 
 * CRITICAL: This addresses the major issue where different parts of the system
 * used conflicting service type values, causing database constraint violations.
 */

import { 
  normalizeServiceType, 
  getServiceDisplayName, 
  getServiceDbValue,
  isValidServiceType,
  migrateLegacyServiceType,
  ServiceTypeConfig
} from './serviceTypeRegistry';

// ============================================================================
// Form Data Standardization
// ============================================================================

/**
 * Standardize service type in form data before submission
 */
export function standardizeFormServiceType(formData: any): any {
  if (!formData) return formData;

  const standardizedData = { ...formData };

  // Handle different possible service type field names
  const serviceTypeFields = ['serviceType', 'service_type', 'type'];
  
  for (const field of serviceTypeFields) {
    if (standardizedData[field]) {
      const originalValue = standardizedData[field];
      const standardizedValue = normalizeServiceType(originalValue);
      
      // Update the field with standardized value
      standardizedData[field] = standardizedValue;
      
      // Add metadata for tracking
      if (!standardizedData.metadata) {
        standardizedData.metadata = {};
      }
      standardizedData.metadata.originalServiceType = originalValue;
      standardizedData.metadata.standardizedServiceType = standardizedValue;
      
      break; // Only process the first found field
    }
  }

  return standardizedData;
}

/**
 * Prepare service type for database insertion
 */
export function prepareServiceTypeForDb(serviceType: string): string {
  const normalized = normalizeServiceType(serviceType);
  const dbValue = getServiceDbValue(normalized);
  
  // Enhanced validation with detailed logging
  if (!isValidServiceType(normalized)) {
    console.warn(`❌ CRITICAL: Invalid service type detected: "${serviceType}" -> normalized: "${normalized}"`);
    console.warn(`Available service types: construction, residential_post_construction, office, residential, etc.`);
    
    // Try enhanced post-construction detection as fallback
    const input = serviceType.toLowerCase();
    if (input.includes('construction') || input.includes('renovation')) {
      if (input.includes('commercial') || input.includes('office') || input.includes('business')) {
        console.warn(`🔧 FALLBACK: Using 'construction' for commercial post-construction service`);
        return 'construction';
      } else {
        console.warn(`🔧 FALLBACK: Using 'residential_post_construction' for residential service`);
        return 'residential_post_construction';
      }
    }
    
    console.warn(`🔧 FINAL FALLBACK: Using 'residential' as last resort`);
    return 'residential';
  }
  
  console.log(`✅ Service type prepared for DB: "${serviceType}" -> "${dbValue}"`);
  return dbValue;
}

/**
 * Prepare service type for display in UI
 */
export function prepareServiceTypeForDisplay(serviceType: string): string {
  const normalized = normalizeServiceType(serviceType);
  return getServiceDisplayName(normalized);
}

// ============================================================================
// Booking Data Standardization
// ============================================================================

/**
 * Standardize booking data before database insertion
 */
export function standardizeBookingData(bookingData: any): any {
  if (!bookingData) return bookingData;

  const standardized = { ...bookingData };

  // Standardize service type
  if (standardized.service_type) {
    const original = standardized.service_type;
    standardized.service_type = prepareServiceTypeForDb(original);
    
    // Store original value in metadata for reference
    if (!standardized.metadata) {
      standardized.metadata = {};
    }
    standardized.metadata.originalServiceType = original;
    standardized.metadata.standardizedAt = new Date().toISOString();
  }

  // Ensure service details contain the standardized type
  if (standardized.service_details && typeof standardized.service_details === 'object') {
    standardized.service_details.standardizedServiceType = standardized.service_type;
    standardized.service_details.displayName = prepareServiceTypeForDisplay(standardized.service_type);
  }

  return standardized;
}

/**
 * Standardize payment data service type
 */
export function standardizePaymentServiceType(paymentData: any): any {
  if (!paymentData) return paymentData;

  const standardized = { ...paymentData };

  if (standardized.service_type) {
    standardized.service_type = prepareServiceTypeForDb(standardized.service_type);
  }

  if (standardized.formData?.serviceType) {
    standardized.formData.serviceType = prepareServiceTypeForDb(standardized.formData.serviceType);
  }

  return standardized;
}

// ============================================================================
// Migration and Validation Utilities
// ============================================================================

/**
 * Validate and fix service type inconsistencies in existing data
 */
export function validateAndFixServiceType(data: any): {
  isValid: boolean;
  original: string;
  fixed: string;
  issues: string[];
} {
  const issues: string[] = [];
  let original = '';
  let fixed = '';

  // Extract service type from various possible locations
  if (data.service_type) {
    original = data.service_type;
  } else if (data.serviceType) {
    original = data.serviceType;
  } else if (data.type) {
    original = data.type;
  } else {
    issues.push('No service type found in data');
    return { isValid: false, original: '', fixed: 'residential', issues };
  }

  // Attempt to normalize
  try {
    fixed = normalizeServiceType(original);
    
    if (!isValidServiceType(fixed)) {
      issues.push(`Invalid service type after normalization: ${fixed}`);
      fixed = 'residential'; // Safe fallback
    }

    if (original !== fixed) {
      issues.push(`Service type was standardized from '${original}' to '${fixed}'`);
    }

  } catch (error) {
    issues.push(`Error normalizing service type: ${error}`);
    fixed = 'residential';
  }

  return {
    isValid: issues.length === 0 || (issues.length === 1 && issues[0].includes('was standardized')),
    original,
    fixed,
    issues
  };
}

/**
 * Batch migrate legacy service types in data array
 */
export function batchMigrateServiceTypes(dataArray: any[]): {
  migrated: any[];
  errors: Array<{ index: number; error: string; data: any }>;
  summary: { total: number; successful: number; failed: number; };
} {
  const migrated: any[] = [];
  const errors: Array<{ index: number; error: string; data: any }> = [];

  dataArray.forEach((item, index) => {
    try {
      const result = validateAndFixServiceType(item);
      
      if (result.isValid || result.fixed) {
        // Apply the fix
        const migratedItem = { ...item };
        if (migratedItem.service_type) {
          migratedItem.service_type = result.fixed;
        }
        if (migratedItem.serviceType) {
          migratedItem.serviceType = result.fixed;
        }
        
        // Add migration metadata
        migratedItem.migrationInfo = {
          originalServiceType: result.original,
          migratedServiceType: result.fixed,
          issues: result.issues,
          migratedAt: new Date().toISOString()
        };
        
        migrated.push(migratedItem);
      } else {
        errors.push({
          index,
          error: `Failed to migrate service type: ${result.issues.join(', ')}`,
          data: item
        });
      }
    } catch (error) {
      errors.push({
        index,
        error: `Unexpected error during migration: ${error}`,
        data: item
      });
    }
  });

  return {
    migrated,
    errors,
    summary: {
      total: dataArray.length,
      successful: migrated.length,
      failed: errors.length
    }
  };
}

// ============================================================================
// Export Configuration
// ============================================================================

export const ServiceTypeStandardizer = {
  // Form utilities
  standardizeFormServiceType,
  
  // Database utilities
  prepareServiceTypeForDb,
  standardizeBookingData,
  standardizePaymentServiceType,
  
  // Display utilities
  prepareServiceTypeForDisplay,
  
  // Validation and migration
  validateAndFixServiceType,
  batchMigrateServiceTypes,
  
  // Direct access to registry functions
  normalize: normalizeServiceType,
  getDisplayName: getServiceDisplayName,
  getDbValue: getServiceDbValue,
  isValid: isValidServiceType,
  migrate: migrateLegacyServiceType,
  
  // Registry access
  config: ServiceTypeConfig
} as const;

export default ServiceTypeStandardizer;
