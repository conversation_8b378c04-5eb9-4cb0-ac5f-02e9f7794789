import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Building2, Sparkles, Phone, Mail, User,
  ArrowRight, CheckCircle, AlertCircle, Trash2, Brush, Calendar, ChevronLeft, ChevronRight
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { ServiceTypeStandardizer } from '../../../../lib/services/serviceTypeStandardizer';


interface PostConstructionFormData {
  // Service Package
  servicePackage: string;
  
  // Project Details
  projectType: string;
  constructionPhase: string;
  projectSize: number;
  floorsCount: number;
  constructionCompletionDate: string;
  propertyAddress: string;
  
  // Cleaning Requirements
  debrisTypes: string[];
  cleaningAreas: string[];
  surfaceTypes: string[];
  specialRequirements: string[];
  
  // Timeline & Access
  serviceUrgency: string;
  preferredStartDate: string;
  siteAccessHours: string;
  securityRequirements: string;
  parkingAvailable: boolean;
  
  // Additional Services
  additionalServices: string[];
  
  // Contact Information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  companyName: string;
  jobTitle: string;
  contractorName?: string;
  projectManager?: string;
  
  // Special Instructions
  specialInstructions: string;
  budgetRange: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  companyName?: string;
  propertyAddress?: string;
  projectType?: string;
  constructionPhase?: string;
  projectSize?: string;
  serviceUrgency?: string;
  preferredStartDate?: string;
}

const steps = [
  { id: 1, name: 'Service Package' },
  { id: 2, name: 'Project Details' },
  { id: 3, name: 'Cleaning Requirements' },
  { id: 4, name: 'Timeline & Access' },
  { id: 5, name: 'Additional Services' },
  { id: 6, name: 'Contact Information' },
];

const servicePackages = [
  { 
    id: 'rough-clean', 
    name: 'Rough Clean', 
    description: 'Initial cleanup during construction phase',
    icon: <Trash2 />,
    features: ['Debris removal', 'Basic dusting', 'Trash disposal'],
    basePrice: 200
  },
  { 
    id: 'final-clean', 
    name: 'Final Clean', 
    description: 'Complete post-construction cleaning',
    icon: <Sparkles />,
    features: ['Deep cleaning', 'Detail work', 'Final inspection'],
    basePrice: 400
  },
  { 
    id: 'touch-up', 
    name: 'Touch-Up Clean', 
    description: 'Minor cleanup after final inspection',
    icon: <Brush />,
    features: ['Spot cleaning', 'Touch-ups', 'Quick turnaround'],
    basePrice: 150
  },
  { 
    id: 'premium-package', 
    name: 'Premium Package', 
    description: 'Complete interior and exterior post-construction cleanup',
    icon: <Building2 />,
    features: ['Full interior clean', 'Exterior cleanup', 'Window cleaning', 'Pressure washing'],
    basePrice: 800
  }
];

const projectTypes = [
  { id: 'new-construction', name: 'New Construction' },
  { id: 'renovation', name: 'Renovation/Remodel' },
  { id: 'addition', name: 'Addition/Extension' },
  { id: 'tenant-improvement', name: 'Tenant Improvement' },
  { id: 'demolition', name: 'Demolition & Rebuild' },
  { id: 'commercial-buildout', name: 'Commercial Build-Out' },
];

const constructionPhases = [
  { id: 'rough-construction', name: 'Rough Construction (In Progress)' },
  { id: 'pre-drywall', name: 'Pre-Drywall Cleanup' },
  { id: 'post-drywall', name: 'Post-Drywall Cleanup' },
  { id: 'final-phase', name: 'Final Phase Cleanup' },
  { id: 'project-complete', name: 'Project Complete' },
  { id: 'pre-occupancy', name: 'Pre-Occupancy Final Clean' },
];

const debrisTypes = [
  { id: 'drywall-dust', name: 'Drywall Dust' },
  { id: 'construction-debris', name: 'Construction Debris' },
  { id: 'paint-residue', name: 'Paint & Primer Residue' },
  { id: 'adhesive-residue', name: 'Adhesive & Caulk Residue' },
  { id: 'metal-shavings', name: 'Metal Shavings' },
  { id: 'wood-sawdust', name: 'Wood Sawdust' },
  { id: 'concrete-dust', name: 'Concrete Dust' },
  { id: 'tile-grout', name: 'Tile & Grout Residue' },
];

const cleaningAreas = [
  { id: 'floors', name: 'All Floors' },
  { id: 'walls', name: 'Walls & Surfaces' },
  { id: 'windows', name: 'Windows & Glass' },
  { id: 'fixtures', name: 'Light Fixtures' },
  { id: 'hvac-vents', name: 'HVAC Vents & Ducts' },
  { id: 'bathrooms', name: 'Bathrooms/Restrooms' },
  { id: 'kitchen-areas', name: 'Kitchen/Break Areas' },
  { id: 'exterior', name: 'Exterior Areas' },
];

const serviceUrgencies = [
  { id: 'standard', name: 'Standard (5-7 days)' },
  { id: 'priority', name: 'Priority (2-3 days)' },
  { id: 'rush', name: 'Rush (24-48 hours)' },
  { id: 'emergency', name: 'Emergency (Same day)' },
];

const additionalServices = [
  { id: 'deep-sanitization', name: 'Deep Sanitization & Disinfection' },
  { id: 'hvac-cleaning', name: 'HVAC System Cleaning' },
  { id: 'pressure-washing', name: 'Exterior Pressure Washing' },
  { id: 'window-cleaning', name: 'Professional Window Cleaning' },
  { id: 'floor-refinishing', name: 'Floor Refinishing/Polishing' },
  { id: 'carpet-cleaning', name: 'Deep Carpet Cleaning' },
  { id: 'move-in-ready', name: 'Move-In Ready Package' },
  { id: 'ongoing-maintenance', name: 'Ongoing Maintenance Plan' },
];

// Enhanced Email Validation
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Enhanced Phone Number Formatting
const formatPhoneNumber = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  } else if (cleaned.length === 11 && cleaned[0] === '1') {
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  return phone;
};

// Phone Number Validation
const validatePhoneNumber = (phone: string): boolean => {
  const cleaned = phone.replace(/\D/g, '');
  return cleaned.length === 10 || (cleaned.length === 11 && cleaned[0] === '1');
};

// Address Validation
const validateAddress = (address: string): boolean => {
  return address.trim().length >= 10 && /\d/.test(address) && /[a-zA-Z]/.test(address);
};

// Modern custom select component
const ModernSelect = ({ label, value, onChange, options, placeholder }: { 
  label: string, 
  value: string, 
  onChange: (value: string) => void, 
  options: { id: string, name: string }[], 
  placeholder: string 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div className="space-y-2">
      <label className="text-sm font-semibold text-white block">{label}</label>
      <div className={`relative ${isOpen ? 'z-20' : ''}`}>
        <motion.button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-left text-white focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300 flex items-center justify-between"
          whileTap={{ scale: 0.98 }}
        >
          <span className={value ? 'text-white' : 'text-gray-400'}>
            {value ? options.find(opt => opt.id === value)?.name : placeholder}
          </span>
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ArrowRight className="w-5 h-5 rotate-90" />
          </motion.div>
        </motion.button>
        
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full left-0 right-0 mt-2 bg-slate-900/80 backdrop-blur-lg border border-white/20 rounded-xl overflow-hidden z-50 shadow-2xl"
            >
              {options.map((option) => (
                <motion.button
                  key={option.id}
                  type="button"
                  onClick={() => {
                    onChange(option.id);
                    setIsOpen(false);
                  }}
                  className="w-full p-4 text-left text-white [text-shadow:0_1px_2px_rgb(0_0_0_/_0.5)] hover:bg-emerald-500/20 transition-colors duration-200 border-b border-white/10 last:border-b-0"
                >
                  {option.name}
                </motion.button>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

const BrandAlignedPostConstructionForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(() => new Date());
  const [showCompletionDatePicker, setShowCompletionDatePicker] = useState(false);
  const [currentCompletionMonth, setCurrentCompletionMonth] = useState(() => new Date());

  // Add minimal custom CSS for date picker functionality
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      /* Ensure date picker works properly */
      input[type="date"] {
        color-scheme: dark;
        position: relative;
      }
      
      /* Make sure calendar icon is clickable */
      input[type="date"]::-webkit-calendar-picker-indicator {
        background: transparent;
        cursor: pointer;
        padding: 4px;
        filter: invert(1) brightness(1.2);
      }
      
      /* Ensure the input is clickable everywhere */
      input[type="date"]::-webkit-datetime-edit {
        color: white;
        cursor: pointer;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [formData, setFormData] = useState<Partial<PostConstructionFormData>>({
    servicePackage: 'final-clean',
    debrisTypes: [],
    cleaningAreas: [],
    surfaceTypes: [],
    specialRequirements: [],
    additionalServices: [],
    parkingAvailable: false,
    projectSize: 1000,
    floorsCount: 1,
  });

  // Save form data to localStorage on changes
  useEffect(() => {
    localStorage.setItem('postConstructionFormData', JSON.stringify(formData));
  }, [formData]);

  // Load form data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem('postConstructionFormData');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        setFormData(prev => ({ ...prev, ...parsedData }));
      } catch (error) {
        console.error('Error loading saved form data:', error);
      }
    }
  }, []);

  // Enhanced form field validation
  const validateField = (fieldName: keyof FormErrors, value: string): string | undefined => {
    switch (fieldName) {
      case 'email':
        if (!value) return 'Email is required';
        if (!validateEmail(value)) return 'Please enter a valid email address';
        break;
      case 'phone':
        if (!value) return 'Phone number is required';
        if (!validatePhoneNumber(value)) return 'Please enter a valid phone number';
        break;
      case 'firstName':
        if (!value || value.trim().length < 2) return 'First name must be at least 2 characters';
        break;
      case 'lastName':
        if (!value || value.trim().length < 2) return 'Last name must be at least 2 characters';
        break;
      case 'companyName':
        if (!value || value.trim().length < 2) return 'Company name is required';
        break;
      case 'propertyAddress':
        if (!value) return 'Property address is required';
        if (!validateAddress(value)) return 'Please enter a complete address with street number and name';
        break;
      case 'projectType':
        if (!value) return 'Please select a project type';
        break;
      case 'constructionPhase':
        if (!value) return 'Please select a construction phase';
        break;
      case 'projectSize':
        if (!value || parseInt(value) < 100) return 'Project size must be at least 100 sq ft';
        break;
      case 'serviceUrgency':
        if (!value) return 'Please select service urgency';
        break;
      case 'preferredStartDate':
        if (!value) return 'Please select a preferred start date';
        break;
    }
    return undefined;
  };

  // Calculate estimated price based on package and size
  const calculateEstimatedPrice = (): number => {
    const selectedPackage = servicePackages.find(pkg => pkg.id === formData.servicePackage);
    const basePrice = selectedPackage?.basePrice || 400;
    
    // Size multiplier
    const sizeMultiplier = Math.max(1, (formData.projectSize || 1000) / 1000);
    
    // Urgency multiplier
    const urgencyMultipliers = {
      'standard': 1,
      'priority': 1.2,
      'rush': 1.5,
      'emergency': 2
    };
    const urgencyMultiplier = urgencyMultipliers[formData.serviceUrgency as keyof typeof urgencyMultipliers] || 1;
    
    // Additional services cost
    const additionalServicesCost = (formData.additionalServices?.length || 0) * 100;
    
    return Math.round((basePrice * sizeMultiplier * urgencyMultiplier) + additionalServicesCost);
  };

  // Enhanced handleNext with validation
  const handleNext = () => {
    if (currentStep < steps.length) {
      setIsLoading(true);
      
      // Validate current step
      const errors: FormErrors = {};
      
      if (currentStep === 2) {
        errors.projectType = validateField('projectType', formData.projectType || '');
        errors.constructionPhase = validateField('constructionPhase', formData.constructionPhase || '');
        errors.projectSize = validateField('projectSize', formData.projectSize?.toString() || '');
        errors.propertyAddress = validateField('propertyAddress', formData.propertyAddress || '');
      } else if (currentStep === 4) {
        errors.serviceUrgency = validateField('serviceUrgency', formData.serviceUrgency || '');
        errors.preferredStartDate = validateField('preferredStartDate', formData.preferredStartDate || '');
      } else if (currentStep === 6) {
        errors.firstName = validateField('firstName', formData.firstName || '');
        errors.lastName = validateField('lastName', formData.lastName || '');
        errors.email = validateField('email', formData.email || '');
        errors.phone = validateField('phone', formData.phone || '');
        errors.companyName = validateField('companyName', formData.companyName || '');
      }
      
      // Filter out undefined errors
      const filteredErrors = Object.fromEntries(
        Object.entries(errors).filter((entry) => entry[1] !== undefined)
      );
      
      setFormErrors(filteredErrors);
      
      if (Object.keys(filteredErrors).length === 0) {
        setTimeout(() => {
          setCurrentStep(currentStep + 1);
          setIsLoading(false);
        }, 500);
      } else {
        setIsLoading(false);
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return formData.servicePackage;
      case 2:
        return formData.projectType && 
               formData.constructionPhase && 
               formData.projectSize && 
               formData.propertyAddress &&
               !formErrors.projectType &&
               !formErrors.constructionPhase &&
               !formErrors.projectSize &&
               !formErrors.propertyAddress;
      case 4:
        return formData.serviceUrgency && 
               formData.preferredStartDate &&
               !formErrors.serviceUrgency &&
               !formErrors.preferredStartDate;
      case 6:
        return formData.firstName && 
               formData.lastName && 
               formData.email && 
               formData.phone && 
               formData.companyName &&
               !formErrors.firstName &&
               !formErrors.lastName &&
               !formErrors.email &&
               !formErrors.phone &&
               !formErrors.companyName;
      default:
        return true;
    }
  };

  // Enhanced input handlers with validation
  const handleEmailChange = (value: string) => {
    setFormData({...formData, email: value});
    const error = validateField('email', value);
    setFormErrors(prev => ({ ...prev, email: error }));
  };

  const handlePhoneChange = (value: string) => {
    const formatted = formatPhoneNumber(value);
    setFormData({...formData, phone: formatted});
    const error = validateField('phone', value);
    setFormErrors(prev => ({ ...prev, phone: error }));
  };

  const handleAddressChange = (value: string) => {
    setFormData({...formData, propertyAddress: value});
    const error = validateField('propertyAddress', value);
    setFormErrors(prev => ({ ...prev, propertyAddress: error }));
  };

  const handleNameChange = (field: 'firstName' | 'lastName', value: string) => {
    setFormData({...formData, [field]: value});
    const error = validateField(field, value);
    setFormErrors(prev => ({ ...prev, [field]: error }));
  };

  const handleCompanyNameChange = (value: string) => {
    setFormData({...formData, companyName: value});
    const error = validateField('companyName', value);
    setFormErrors(prev => ({ ...prev, companyName: error }));
  };

  // Toggle handlers for multi-select fields
  const handleDebrisTypeToggle = (debrisId: string) => {
    const currentTypes = formData.debrisTypes || [];
    if (currentTypes.includes(debrisId)) {
      setFormData({
        ...formData,
        debrisTypes: currentTypes.filter(id => id !== debrisId)
      });
    } else {
      setFormData({
        ...formData,
        debrisTypes: [...currentTypes, debrisId]
      });
    }
  };

  const handleCleaningAreaToggle = (areaId: string) => {
    const currentAreas = formData.cleaningAreas || [];
    if (currentAreas.includes(areaId)) {
      setFormData({
        ...formData,
        cleaningAreas: currentAreas.filter(id => id !== areaId)
      });
    } else {
      setFormData({
        ...formData,
        cleaningAreas: [...currentAreas, areaId]
      });
    }
  };

  const handleAdditionalServiceToggle = (serviceId: string) => {
    const currentServices = formData.additionalServices || [];
    if (currentServices.includes(serviceId)) {
      setFormData({
        ...formData,
        additionalServices: currentServices.filter(id => id !== serviceId)
      });
    } else {
      setFormData({
        ...formData,
        additionalServices: [...currentServices, serviceId]
      });
    }
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      // Validate all contact fields before submission
      const contactErrors: FormErrors = {};
      contactErrors.firstName = validateField('firstName', formData.firstName || '');
      contactErrors.lastName = validateField('lastName', formData.lastName || '');
      contactErrors.email = validateField('email', formData.email || '');
      contactErrors.phone = validateField('phone', formData.phone || '');
      contactErrors.companyName = validateField('companyName', formData.companyName || '');
      
      const filteredContactErrors = Object.fromEntries(
        Object.entries(contactErrors).filter((entry) => entry[1] !== undefined)
      );
      
      if (Object.keys(filteredContactErrors).length > 0) {
        setFormErrors(filteredContactErrors);
        setIsLoading(false);
        return;
      }

      // Submit form data to Supabase database
      const { supabase } = await import('../../../../lib/supabase/client');
      
      // Standardize and validate service type using enhanced backend infrastructure
      const serviceType = ServiceTypeStandardizer.prepareServiceTypeForDb('construction');
      
      // Validate service type before proceeding
      if (!ServiceTypeStandardizer.isValid(serviceType)) {
        throw new Error(`Invalid commercial service type: ${serviceType}`);
      }
      
      const bookingData = {
        user_id: user?.id || null,
        service_type: serviceType,
        property_details: {
          projectType: formData.projectType,
          constructionPhase: formData.constructionPhase,
          address: formData.propertyAddress,
          projectSize: formData.projectSize,
          floorsCount: formData.floorsCount,
          constructionCompletionDate: formData.constructionCompletionDate,
          siteAccessHours: formData.siteAccessHours,
          securityRequirements: formData.securityRequirements,
          parkingAvailable: formData.parkingAvailable,
        },
        service_details: {
          servicePackage: formData.servicePackage,
          debrisTypes: formData.debrisTypes,
          cleaningAreas: formData.cleaningAreas,
          surfaceTypes: formData.surfaceTypes,
          specialRequirements: formData.specialRequirements,
          additionalServices: formData.additionalServices,
          budgetRange: formData.budgetRange,
        },
        schedule: {
          serviceUrgency: formData.serviceUrgency,
          preferredStartDate: formData.preferredStartDate,
          siteAccessHours: formData.siteAccessHours,
        },
        contact: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          companyName: formData.companyName,
          jobTitle: formData.jobTitle,
          contractorName: formData.contractorName,
          projectManager: formData.projectManager,
        },
        status: 'pending',
        total_price: calculateEstimatedPrice(),
        special_instructions: formData.specialInstructions,
        metadata: {
          submittedAt: new Date().toISOString(),
          requestType: 'estimate',
        }
      };

      // Use ServiceTypeStandardizer to standardize booking data before submission
      const standardizedBookingData = ServiceTypeStandardizer.standardizeBookingData(bookingData);
      
      console.log('✅ Commercial Post-Construction: Submitting standardized booking data:', {
        service_type: standardizedBookingData.service_type,
        originalServiceType: standardizedBookingData.metadata?.originalServiceType,
        standardizedAt: standardizedBookingData.metadata?.standardizedAt
      });

      const { data, error } = await supabase!
        .from('booking_forms')
        .insert([standardizedBookingData])
        .select()
        .single();

      if (error) {
        console.error('Error saving booking:', error);
        throw new Error('Failed to save booking');
      }

      console.log('✅ Commercial Post-Construction booking saved successfully:', data);

      // Clear form data from localStorage after successful submission
      localStorage.removeItem('postConstructionFormData');
      
      // Show success popup
      setShowSuccessPopup(true);
      setIsLoading(false);
    } catch (error) {
      console.error('❌ Commercial Post-Construction Error submitting form:', error);
      const errorMessage = error instanceof Error ? error.message : 'Please try again.';
      
      // Enhanced error handling with user feedback
      alert(`Unable to submit your commercial post-construction cleaning request: ${errorMessage}`);
      setIsLoading(false);
    }
  };

  const handleClosePopup = () => {
    setShowSuccessPopup(false);
    localStorage.removeItem('postConstructionFormData');
    
    if (user) {
      navigate('/accountdashboard');
    } else {
      navigate('/');
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen flex flex-col items-center justify-start py-8 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-4xl mx-auto">
          {/* Modern Progress Bar */}
          <div className="mb-12">
            {/* Step Counter */}
            <div className="flex items-center justify-center mb-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-full px-6 py-2 border border-white/20">
                <span className="text-white font-medium">
                  Step {currentStep} of {steps.length}
                </span>
              </div>
            </div>
            
            {/* Progress Line */}
            <div className="relative bg-white/10 h-2 rounded-full mb-6 overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-emerald-400 to-green-500 rounded-full"
                initial={{ width: '16.67%' }}
                animate={{ width: `${(currentStep / steps.length) * 100}%` }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              />
            </div>
            
            {/* Step Indicators */}
            <div className="flex justify-between">
              {steps.map((step) => (
                <div key={step.id} className="flex flex-col items-center">
                  <motion.div
                    className={`w-4 h-4 rounded-full border-2 ${
                      currentStep >= step.id 
                        ? 'bg-emerald-400 border-emerald-400' 
                        : 'bg-white/10 border-white/30'
                    }`}
                    animate={{
                      scale: currentStep === step.id ? 1.2 : 1,
                    }}
                    transition={{ duration: 0.3 }}
                  />
                  <span className={`text-xs mt-2 font-medium ${
                    currentStep >= step.id ? 'text-emerald-400' : 'text-white/60'
                  }`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Form Card */}
          <motion.div
            className="backdrop-blur-[30px] bg-white/[0.08] border border-white/20 rounded-3xl p-8 shadow-2xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <AnimatePresence mode="wait">
              {/* Step 1: Service Package */}
              {currentStep === 1 && (
                <motion.div
                  key="step1"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Choose Your Service Package</h2>
                  <p className="text-gray-300 mb-6">Select the post-construction cleaning package that best fits your needs</p>

                  <div className="grid gap-4">
                    {servicePackages.map((pkg) => (
                      <motion.button
                        key={pkg.id}
                        type="button"
                        onClick={() => setFormData({...formData, servicePackage: pkg.id})}
                        className={`p-6 rounded-xl border-2 transition-all duration-300 text-left ${
                          formData.servicePackage === pkg.id
                            ? 'bg-emerald-500/20 border-emerald-400 shadow-lg shadow-emerald-400/20'
                            : 'bg-white/5 border-white/20 hover:border-white/40'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center mb-3">
                          <div className="text-emerald-400 mr-4">
                            {pkg.icon}
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-white">{pkg.name}</h3>
                            <p className="text-emerald-400 font-medium">Starting at ${pkg.basePrice}</p>
                          </div>
                        </div>
                        <p className="text-gray-300 mb-3">{pkg.description}</p>
                        <div className="flex flex-wrap gap-2">
                          {pkg.features.map((feature, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 bg-white/10 rounded-full text-sm text-white"
                            >
                              {feature}
                            </span>
                          ))}
                        </div>
                      </motion.button>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Step 2: Project Details */}
              {currentStep === 2 && (
                <motion.div
                  key="step2"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-3xl font-bold text-white mb-3 tracking-wide">Project Details</h2>
                  <p className="text-slate-300 mb-8 text-lg">Tell us about your construction project</p>
                  
                  <div className="space-y-6">
                    {/* Basic Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <ModernSelect
                        label="Project Type"
                        value={formData.projectType || ''}
                        onChange={(value) => setFormData({...formData, projectType: value})}
                        options={projectTypes}
                        placeholder="Select project type"
                      />
                      <ModernSelect
                        label="Construction Phase"
                        value={formData.constructionPhase || ''}
                        onChange={(value) => setFormData({...formData, constructionPhase: value})}
                        options={constructionPhases}
                        placeholder="Select construction phase"
                      />
                    </div>

                    {/* Project Size Details */}
                    <div>
                      <h3 className="text-xl font-bold text-white mb-4 tracking-wide">Project Size Details</h3>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                        {/* Project Size - Typeable Input */}
                        <div className="space-y-3">
                          <label className="text-sm font-bold text-white block tracking-wide">Project Size</label>
                          <div className="relative group">
                            <input 
                              type="number" 
                              placeholder="e.g. 5000"
                              value={formData.projectSize || ''}
                              onChange={(e) => setFormData({...formData, projectSize: parseInt(e.target.value) || 0})}
                              className="w-full p-4 rounded-2xl border-2 border-slate-600/30 bg-slate-800/30 backdrop-blur-xl text-white text-lg font-bold placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 hover:bg-slate-800/40 hover:border-slate-500/50 transition-all duration-300 shadow-2xl"
                            />
                            <span className="absolute right-4 top-1/2 -translate-y-1/2 text-slate-400 text-sm font-semibold">sq ft</span>
                            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-emerald-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                          </div>
                        </div>
                        
                        {/* Number of Floors - Editable Input */}
                        <div className="space-y-3">
                          <label className="text-sm font-bold text-white block tracking-wide">Number of Floors</label>
                          <div className="relative group">
                            <input 
                              type="number" 
                              placeholder="e.g. 2"
                              min="1"
                              value={formData.floorsCount || ''}
                              onChange={(e) => setFormData({...formData, floorsCount: parseInt(e.target.value) || 1})}
                              className="w-full p-4 rounded-2xl border-2 border-slate-600/30 bg-slate-800/30 backdrop-blur-xl text-white text-lg font-bold placeholder-slate-400 focus:outline-none focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 hover:bg-slate-800/40 hover:border-slate-500/50 transition-all duration-300 shadow-2xl"
                            />
                            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-emerald-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Location & Timeline */}
                    <div>
                      <h3 className="text-xl font-bold text-white mb-4 tracking-wide">Location & Timeline</h3>
                      <div className="space-y-4">
                        <div className="space-y-3">
                          <label className="text-sm font-bold text-white block tracking-wide">
                            Property Address <span className="text-red-400 font-bold">*</span>
                          </label>
                          <div className="relative group">
                            <input 
                              type="text" 
                              placeholder="123 Construction St, City, State 12345" 
                              className={`w-full p-4 rounded-2xl border-2 bg-slate-800/30 backdrop-blur-xl text-white placeholder-slate-400 focus:outline-none hover:bg-slate-800/40 transition-all duration-300 shadow-2xl ${
                                formErrors.propertyAddress 
                                  ? 'border-red-400/70 focus:border-red-400 focus:ring-2 focus:ring-red-400/20' 
                                  : 'border-slate-600/30 focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 hover:border-slate-500/50'
                              }`}
                              value={formData.propertyAddress || ''}
                              onChange={(e) => handleAddressChange(e.target.value)}
                            />
                            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-emerald-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                          </div>
                          {formErrors.propertyAddress && (
                            <motion.div
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="flex items-center gap-2 text-red-400 text-sm font-semibold"
                            >
                              <AlertCircle className="w-4 h-4" />
                              {formErrors.propertyAddress}
                            </motion.div>
                          )}
                        </div>
                        <div className="space-y-3">
                          <label className="text-sm font-bold text-white block tracking-wide">
                            Construction Completion Date (Expected)
                          </label>
                          <div className="relative">
                            <motion.button
                              type="button"
                              onClick={() => setShowCompletionDatePicker(!showCompletionDatePicker)}
                              className="w-full bg-slate-800/30 backdrop-blur-xl p-4 rounded-xl border-2 border-slate-700 text-white focus:outline-none focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 hover:bg-slate-800/40 hover:border-slate-500/50 transition-all duration-300 shadow-lg text-left flex items-center justify-between"
                              whileTap={{ scale: 0.98 }}
                            >
                              <span className={formData.constructionCompletionDate ? 'text-white font-semibold' : 'text-gray-400'}>
                                {formData.constructionCompletionDate 
                                  ? new Date(formData.constructionCompletionDate).toLocaleDateString('en-US', { 
                                      weekday: 'long', 
                                      year: 'numeric', 
                                      month: 'long', 
                                      day: 'numeric' 
                                    })
                                  : 'Select expected completion date'
                                }
                              </span>
                              <Calendar className="w-5 h-5 text-gray-400" />
                            </motion.button>
                            
                            <AnimatePresence>
                              {showCompletionDatePicker && (
                                <motion.div
                                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                                  animate={{ opacity: 1, y: 0, scale: 1 }}
                                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                                  transition={{ duration: 0.2 }}
                                  className="absolute top-full left-0 right-0 mt-2 bg-slate-900/90 backdrop-blur-xl border-2 border-slate-700 rounded-2xl overflow-hidden z-50 shadow-2xl p-6"
                                >
                                  <div className="space-y-4">
                                    {/* Month Navigation */}
                                    <div className="flex items-center justify-between">
                                                                             <motion.button
                                         type="button"
                                         onClick={() => {
                                           const newMonth = new Date(currentCompletionMonth);
                                           newMonth.setMonth(newMonth.getMonth() - 1);
                                           setCurrentCompletionMonth(newMonth);
                                         }}
                                         className="p-2 rounded-lg hover:bg-slate-800/50 transition-colors"
                                         whileTap={{ scale: 0.95 }}
                                       >
                                         <ChevronLeft className="w-5 h-5 text-white" />
                                       </motion.button>
                                       <h3 className="text-lg font-bold text-white">
                                         {currentCompletionMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                                       </h3>
                                       <motion.button
                                         type="button"
                                         onClick={() => {
                                           const newMonth = new Date(currentCompletionMonth);
                                           newMonth.setMonth(newMonth.getMonth() + 1);
                                           setCurrentCompletionMonth(newMonth);
                                         }}
                                         className="p-2 rounded-lg hover:bg-slate-800/50 transition-colors"
                                         whileTap={{ scale: 0.95 }}
                                       >
                                         <ChevronRight className="w-5 h-5 text-white" />
                                       </motion.button>
                                    </div>
                                    
                                    {/* Calendar Grid */}
                                    <div className="grid grid-cols-7 gap-2">
                                      {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (
                                        <div key={day} className="text-center text-sm font-semibold text-gray-400 p-3">
                                          {day}
                                        </div>
                                      ))}
                                      {(() => {
                                        const firstDay = new Date(currentCompletionMonth.getFullYear(), currentCompletionMonth.getMonth(), 1);
                                        const startDate = new Date(firstDay);
                                        startDate.setDate(startDate.getDate() - firstDay.getDay());
                                        
                                        const days = [];
                                        const today = new Date();
                                        
                                        for (let i = 0; i < 42; i++) {
                                          const date = new Date(startDate);
                                          date.setDate(startDate.getDate() + i);
                                          
                                          const isCurrentMonth = date.getMonth() === currentCompletionMonth.getMonth();
                                          const isToday = date.toDateString() === today.toDateString();
                                          const isPast = date < today && !isToday;
                                          const isSelected = formData.constructionCompletionDate === date.toISOString().split('T')[0];
                                          
                                          days.push(
                                            <motion.button
                                              key={i}
                                              type="button"
                                              onClick={() => {
                                                if (!isPast) {
                                                  setFormData({...formData, constructionCompletionDate: date.toISOString().split('T')[0]});
                                                  setShowCompletionDatePicker(false);
                                                }
                                              }}
                                              disabled={isPast}
                                              className={`p-3 text-sm rounded-lg transition-all duration-200 min-h-[40px] ${
                                                isSelected
                                                  ? 'bg-emerald-500 text-white font-bold'
                                                  : isPast
                                                  ? 'text-gray-600 cursor-not-allowed'
                                                  : isCurrentMonth
                                                  ? 'text-white hover:bg-emerald-500/20 hover:text-emerald-400'
                                                  : 'text-gray-500 hover:text-gray-400'
                                              } ${isToday ? 'ring-2 ring-emerald-400' : ''}`}
                                              whileHover={!isPast ? { scale: 1.05 } : {}}
                                              whileTap={!isPast ? { scale: 0.95 } : {}}
                                            >
                                              {date.getDate()}
                                            </motion.button>
                                          );
                                        }
                                        
                                        return days;
                                      })()}
                                    </div>
                                  </div>
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Cleaning Requirements */}
              {currentStep === 3 && (
                <motion.div
                  key="step3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Cleaning Requirements</h2>
                  <p className="text-gray-300 mb-6">Specify what needs to be cleaned and removed</p>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-white font-medium mb-3">
                        Types of Debris Present (Select all that apply)
                      </label>
                      <div className="grid md:grid-cols-2 gap-3">
                        {debrisTypes.map((debris) => (
                          <motion.button
                            key={debris.id}
                            type="button"
                            onClick={() => handleDebrisTypeToggle(debris.id)}
                            className={`p-4 rounded-xl border-2 transition-all duration-300 ${
                              formData.debrisTypes?.includes(debris.id)
                                ? 'border-emerald-400 bg-emerald-500/20 text-white'
                                : 'border-white/20 bg-white/5 text-gray-300 hover:border-white/40'
                            }`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <div className="flex items-center justify-between">
                              <span>{debris.name}</span>
                              {formData.debrisTypes?.includes(debris.id) && (
                                <CheckCircle className="w-5 h-5 text-emerald-400" />
                              )}
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-3">
                        Areas Requiring Cleaning (Select all that apply)
                      </label>
                      <div className="grid md:grid-cols-2 gap-3">
                        {cleaningAreas.map((area) => (
                          <motion.button
                            key={area.id}
                            type="button"
                            onClick={() => handleCleaningAreaToggle(area.id)}
                            className={`p-4 rounded-xl border-2 transition-all duration-300 ${
                              formData.cleaningAreas?.includes(area.id)
                                ? 'border-emerald-400 bg-emerald-500/20 text-white'
                                : 'border-white/20 bg-white/5 text-gray-300 hover:border-white/40'
                            }`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <div className="flex items-center justify-between">
                              <span>{area.name}</span>
                              {formData.cleaningAreas?.includes(area.id) && (
                                <CheckCircle className="w-5 h-5 text-emerald-400" />
                              )}
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Step 4: Timeline & Access */}
              {currentStep === 4 && (
                <motion.div
                  key="step4"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Timeline & Site Access</h2>
                  <p className="text-gray-300 mb-6">When can we perform the cleaning service?</p>

                  <div className="space-y-6">
                    <ModernSelect
                      label="Service Urgency *"
                      value={formData.serviceUrgency || ''}
                      onChange={(value) => setFormData({...formData, serviceUrgency: value})}
                      options={serviceUrgencies}
                      placeholder="Select service urgency"
                    />
                    {formErrors.serviceUrgency && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center text-red-400 text-sm"
                      >
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {formErrors.serviceUrgency}
                      </motion.div>
                    )}

                    <div className="space-y-3">
                      <label className="text-sm font-bold text-white block tracking-wide">
                        Preferred Start Date *
                      </label>
                      <div className="relative">
                        <motion.button
                          type="button"
                          onClick={() => setShowDatePicker(!showDatePicker)}
                          className={`w-full bg-slate-800/30 backdrop-blur-xl p-4 rounded-xl border-2 ${
                            formErrors.preferredStartDate ? 'border-red-400/50' : 'border-slate-700'
                          } text-white focus:outline-none focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 hover:bg-slate-800/40 hover:border-slate-500/50 transition-all duration-300 shadow-lg text-left flex items-center justify-between`}
                          whileTap={{ scale: 0.98 }}
                        >
                          <span className={formData.preferredStartDate ? 'text-white font-semibold' : 'text-gray-400'}>
                            {formData.preferredStartDate 
                              ? new Date(formData.preferredStartDate).toLocaleDateString('en-US', { 
                                  weekday: 'long', 
                                  year: 'numeric', 
                                  month: 'long', 
                                  day: 'numeric' 
                                })
                              : 'Select preferred start date'
                            }
                          </span>
                          <Calendar className="w-5 h-5 text-gray-400" />
                        </motion.button>
                        
                        <AnimatePresence>
                          {showDatePicker && (
                            <motion.div
                              initial={{ opacity: 0, y: -10, scale: 0.95 }}
                              animate={{ opacity: 1, y: 0, scale: 1 }}
                              exit={{ opacity: 0, y: -10, scale: 0.95 }}
                              transition={{ duration: 0.2 }}
                              className="absolute top-full left-0 right-0 mt-2 bg-slate-900/90 backdrop-blur-xl border-2 border-slate-700 rounded-2xl overflow-hidden z-50 shadow-2xl p-6"
                            >
                              <div className="space-y-4">
                                {/* Month Navigation */}
                                <div className="flex items-center justify-between">
                                                                     <motion.button
                                     type="button"
                                     onClick={() => {
                                       const newMonth = new Date(currentMonth);
                                       newMonth.setMonth(newMonth.getMonth() - 1);
                                       setCurrentMonth(newMonth);
                                     }}
                                     className="p-2 rounded-lg hover:bg-slate-800/50 transition-colors"
                                     whileTap={{ scale: 0.95 }}
                                   >
                                     <ChevronLeft className="w-5 h-5 text-white" />
                                   </motion.button>
                                   <h3 className="text-lg font-bold text-white">
                                     {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                                   </h3>
                                   <motion.button
                                     type="button"
                                     onClick={() => {
                                       const newMonth = new Date(currentMonth);
                                       newMonth.setMonth(newMonth.getMonth() + 1);
                                       setCurrentMonth(newMonth);
                                     }}
                                     className="p-2 rounded-lg hover:bg-slate-800/50 transition-colors"
                                     whileTap={{ scale: 0.95 }}
                                   >
                                     <ChevronRight className="w-5 h-5 text-white" />
                                   </motion.button>
                                </div>
                                
                                {/* Calendar Grid */}
                                <div className="grid grid-cols-7 gap-2">
                                  {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (
                                    <div key={day} className="text-center text-sm font-semibold text-gray-400 p-3">
                                      {day}
                                    </div>
                                  ))}
                                                                     {(() => {
                                     const firstDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
                                     const startDate = new Date(firstDay);
                                     startDate.setDate(startDate.getDate() - firstDay.getDay());
                                    
                                    const days = [];
                                    const today = new Date();
                                    
                                    for (let i = 0; i < 42; i++) {
                                      const date = new Date(startDate);
                                      date.setDate(startDate.getDate() + i);
                                      
                                      const isCurrentMonth = date.getMonth() === currentMonth.getMonth();
                                      const isToday = date.toDateString() === today.toDateString();
                                      const isPast = date < today && !isToday;
                                      const isSelected = formData.preferredStartDate === date.toISOString().split('T')[0];
                                      
                                      days.push(
                                        <motion.button
                                          key={i}
                                          type="button"
                                          onClick={() => {
                                            if (!isPast) {
                                              setFormData({...formData, preferredStartDate: date.toISOString().split('T')[0]});
                                              setShowDatePicker(false);
                                            }
                                          }}
                                          disabled={isPast}
                                          className={`p-3 text-sm rounded-lg transition-all duration-200 min-h-[40px] ${
                                            isSelected
                                              ? 'bg-emerald-500 text-white font-bold'
                                              : isPast
                                              ? 'text-gray-600 cursor-not-allowed'
                                              : isCurrentMonth
                                              ? 'text-white hover:bg-emerald-500/20 hover:text-emerald-400'
                                              : 'text-gray-500 hover:text-gray-400'
                                          } ${isToday ? 'ring-2 ring-emerald-400' : ''}`}
                                          whileHover={!isPast ? { scale: 1.05 } : {}}
                                          whileTap={!isPast ? { scale: 0.95 } : {}}
                                        >
                                          {date.getDate()}
                                        </motion.button>
                                      );
                                    }
                                    
                                    return days;
                                  })()}
                                </div>
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                      {formErrors.preferredStartDate && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="flex items-center text-red-400 text-sm mt-2"
                        >
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.preferredStartDate}
                        </motion.div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-white block">Site Access Hours</label>
                      <input
                        type="text"
                        value={formData.siteAccessHours || ''}
                        onChange={(e) => setFormData({...formData, siteAccessHours: e.target.value})}
                        className="w-full bg-slate-800/30 backdrop-blur-xl p-4 rounded-xl border-2 border-slate-700 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 transition-all duration-300 shadow-lg"
                        placeholder="e.g., 8AM - 6PM, 24/7 access, etc."
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-white block">Security Requirements</label>
                      <input
                        type="text"
                        value={formData.securityRequirements || ''}
                        onChange={(e) => setFormData({...formData, securityRequirements: e.target.value})}
                        className="w-full bg-slate-800/30 backdrop-blur-xl p-4 rounded-xl border-2 border-slate-700 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 transition-all duration-300 shadow-lg"
                        placeholder="Security badges, escorts, background checks, etc."
                      />
                    </div>

                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="parking"
                        checked={formData.parkingAvailable || false}
                        onChange={(e) => setFormData({...formData, parkingAvailable: e.target.checked})}
                        className="w-5 h-5 text-emerald-400 bg-slate-800/30 border-slate-700 rounded-md focus:ring-emerald-400 focus:ring-offset-slate-900"
                      />
                      <label htmlFor="parking" className="text-white">
                        Parking available on-site for cleaning crew
                      </label>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Step 5: Additional Services */}
              {currentStep === 5 && (
                <motion.div
                  key="step5"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Additional Services</h2>
                  <p className="text-gray-300 mb-6">Enhance your post-construction cleaning with these optional services</p>

                  <div className="grid md:grid-cols-2 gap-3">
                    {additionalServices.map((service) => (
                      <motion.button
                        key={service.id}
                        type="button"
                        onClick={() => handleAdditionalServiceToggle(service.id)}
                        className={`p-4 rounded-xl border-2 transition-all duration-300 text-left ${
                          formData.additionalServices?.includes(service.id)
                            ? 'border-emerald-400 bg-emerald-500/20 text-white'
                            : 'border-white/20 bg-white/5 text-gray-300 hover:border-white/40'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center justify-between">
                          <span>{service.name}</span>
                          {formData.additionalServices?.includes(service.id) && (
                            <CheckCircle className="w-5 h-5 text-emerald-400" />
                          )}
                        </div>
                      </motion.button>
                    ))}
                  </div>

                  <div className="mt-6 space-y-2">
                    <label className="text-sm font-semibold text-white block">Special Instructions</label>
                    <textarea
                      value={formData.specialInstructions || ''}
                      onChange={(e) => setFormData({...formData, specialInstructions: e.target.value})}
                      className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300 h-24 resize-none"
                      placeholder="Any special instructions or requirements for the cleaning crew..."
                    />
                  </div>
                </motion.div>
              )}

              {/* Step 6: Contact Information */}
              {currentStep === 6 && (
                <motion.div
                  key="step6"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Contact Information</h2>
                  <p className="text-gray-300 mb-6">We'll use this information to coordinate your service</p>

                  <div className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="text-sm font-semibold text-white block">
                          First Name *
                        </label>
                        <div className="relative">
                          <input
                            type="text"
                            value={formData.firstName || ''}
                            onChange={(e) => handleNameChange('firstName', e.target.value)}
                            className={`w-full bg-white/10 p-4 pl-12 rounded-xl border ${
                              formErrors.firstName ? 'border-red-400' : 'border-white/20'
                            } text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300`}
                            placeholder="Enter your first name"
                          />
                          <User className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        </div>
                        {formErrors.firstName && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4 mr-1" />
                            {formErrors.firstName}
                          </motion.div>
                        )}
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-semibold text-white block">
                          Last Name *
                        </label>
                        <input
                          type="text"
                          value={formData.lastName || ''}
                          onChange={(e) => handleNameChange('lastName', e.target.value)}
                          className={`w-full bg-white/10 p-4 rounded-xl border ${
                            formErrors.lastName ? 'border-red-400' : 'border-white/20'
                          } text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300`}
                          placeholder="Enter your last name"
                        />
                        {formErrors.lastName && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4 mr-1" />
                            {formErrors.lastName}
                          </motion.div>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-white block">
                        Email Address *
                      </label>
                      <div className="relative">
                        <input
                          type="email"
                          value={formData.email || ''}
                          onChange={(e) => handleEmailChange(e.target.value)}
                          className={`w-full bg-white/10 p-4 pl-12 rounded-xl border ${
                            formErrors.email ? 'border-red-400' : 'border-white/20'
                          } text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300`}
                          placeholder="Enter your email address"
                        />
                        <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      </div>
                      {formErrors.email && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="flex items-center text-red-400 text-sm"
                        >
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.email}
                        </motion.div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-white block">
                        Phone Number *
                      </label>
                      <div className="relative">
                        <input
                          type="tel"
                          value={formData.phone || ''}
                          onChange={(e) => handlePhoneChange(e.target.value)}
                          className={`w-full bg-white/10 p-4 pl-12 rounded-xl border ${
                            formErrors.phone ? 'border-red-400' : 'border-white/20'
                          } text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300`}
                          placeholder="Enter your phone number"
                        />
                        <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      </div>
                      {formErrors.phone && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="flex items-center text-red-400 text-sm"
                        >
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.phone}
                        </motion.div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-white block">
                        Company Name *
                      </label>
                      <input
                        type="text"
                        value={formData.companyName || ''}
                        onChange={(e) => handleCompanyNameChange(e.target.value)}
                        className={`w-full bg-white/10 p-4 rounded-xl border ${
                          formErrors.companyName ? 'border-red-400' : 'border-white/20'
                        } text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300`}
                        placeholder="Enter company name"
                      />
                      {formErrors.companyName && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="flex items-center text-red-400 text-sm"
                        >
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.companyName}
                        </motion.div>
                      )}
                    </div>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="text-sm font-semibold text-white block">Job Title</label>
                        <input
                          type="text"
                          value={formData.jobTitle || ''}
                          onChange={(e) => setFormData({...formData, jobTitle: e.target.value})}
                          className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300"
                          placeholder="Your job title"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-semibold text-white block">Project Manager</label>
                        <input
                          type="text"
                          value={formData.projectManager || ''}
                          onChange={(e) => setFormData({...formData, projectManager: e.target.value})}
                          className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300"
                          placeholder="Project manager name"
                        />
                      </div>
                    </div>

                    {/* Estimated Price Display */}
                    <div className="mt-8 p-6 bg-emerald-500/10 border border-emerald-400/30 rounded-xl">
                      <h3 className="text-lg font-semibold text-emerald-400 mb-2">Estimated Service Cost</h3>
                      <div className="text-3xl font-bold text-white">${calculateEstimatedPrice()}</div>
                      <p className="text-sm text-gray-300 mt-2">
                        Final price will be confirmed after site inspection
                      </p>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Navigation Buttons */}
            <div className="flex justify-between items-center mt-8 pt-6 border-t border-white/20">
              <Button
                variant="outline"
                onClick={handleBack}
                disabled={currentStep === 1 || isLoading}
                className="text-white border-white/30 hover:bg-white/10"
              >
                Back
              </Button>

              <div className="text-white/60 text-sm">
                Step {currentStep} of {steps.length}
              </div>

              {currentStep < steps.length ? (
                <Button
                  onClick={handleNext}
                  disabled={!isStepValid() || isLoading}
                  className="bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white"
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Loading...
                    </div>
                  ) : (
                    <>
                      Next
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  disabled={!isStepValid() || isLoading}
                  className="bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white"
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Processing...
                    </div>
                  ) : (
                    <>
                      Get Quote
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </>
                  )}
                </Button>
              )}
            </div>
          </motion.div>
        </div>

        {/* Success Popup */}
        <AnimatePresence>
          {showSuccessPopup && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            >
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.8, opacity: 0 }}
                className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-8 max-w-md w-full text-center"
              >
                <div className="w-16 h-16 bg-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Quote Submitted Successfully!</h3>
                <p className="text-gray-300 mb-6">
                  We'll review your post-construction cleaning requirements and contact you within 24 hours with a detailed quote.
                </p>
                <Button
                  onClick={handleClosePopup}
                  className="w-full bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white"
                >
                  Back to Commercial Services
                </Button>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>


      </div>
    </AnimatedBackground>
  );
};

export default BrandAlignedPostConstructionForm; 
