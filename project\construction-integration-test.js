#!/usr/bin/env node

/**
 * CONSTRUCTION FORM INTEGRATION TESTER
 * 
 * Complete end-to-end validation of construction service forms:
 * - Residential Post-Construction Form
 * - Commercial Construction Form
 * - Data persistence and retrieval
 * - Dashboard display validation
 * - Price calculation verification
 * - Error handling testing
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('❌ ERROR: Missing Supabase configuration');
    console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your .env file');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

console.log('🏗️ CONSTRUCTION FORM INTEGRATION TESTER');
console.log('=' .repeat(60));

// Test results collection
const testResults = {
    total: 0,
    passed: 0,
    failed: 0,
    tests: []
};

// Helper function to log test results
function logTest(name, passed, details = '') {
    testResults.total++;
    if (passed) {
        testResults.passed++;
        console.log(`✅ PASS: ${name}`);
    } else {
        testResults.failed++;
        console.log(`❌ FAIL: ${name}`);
        if (details) console.log(`   Details: ${details}`);
    }
    testResults.tests.push({ name, passed, details });
}

// Test data generators
function generateResidentialPostConstructionData() {
    return {
        user_id: null, // Simulate guest user
        service_type: 'residential_post_construction',
        status: 'quote_requested',
        contact: {
            firstName: 'John',
            lastName: 'Smith',
            email: '<EMAIL>',
            phone: '(*************'
        },
        property_details: {
            type: 'house',
            size: 'medium',
            address: '123 Construction Lane',
            city: 'Austin',
            zipCode: '78701'
        },
        service_details: {
            constructionType: 'renovation',
            cleaningServices: ['dust-removal', 'window-cleaning', 'floor-cleaning'],
            cleaningTimeline: '1-week',
            serviceSubType: 'residential_post_construction',
            totalPrice: 350,
            actualServiceType: 'residential_post_construction',
            specialInstructions: 'Recently completed kitchen renovation',
            submittedAt: new Date().toISOString(),
            source: 'integration_test',
            requestType: 'estimate'
        },
        schedule: {
            preferredDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            cleaningTimeline: '1-week'
        }
    };
}

function generateCommercialConstructionData() {
    return {
        user_id: null, // Simulate guest user
        service_type: 'construction',
        status: 'pending',
        contact: {
            firstName: 'Sarah',
            lastName: 'Johnson',
            email: '<EMAIL>',
            phone: '(*************',
            companyName: 'Business Corp',
            jobTitle: 'Facilities Manager'
        },
        property_details: {
            projectType: 'new-construction',
            constructionPhase: 'final-phase',
            address: '456 Commercial Blvd',
            projectSize: 5000,
            floorsCount: 3
        },
        service_details: {
            servicePackage: 'final-clean',
            debrisTypes: ['drywall-dust', 'construction-debris', 'paint-residue'],
            cleaningAreas: ['floors', 'walls', 'windows', 'fixtures'],
            additionalServices: ['deep-sanitization', 'pressure-washing'],
            budgetRange: '$1000-2000'
        },
        schedule: {
            serviceUrgency: 'priority',
            preferredStartDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        },
        total_price: 800,
        metadata: {
            submittedAt: new Date().toISOString(),
            requestType: 'estimate'
        }
    };
}

// Service type normalization simulation (based on actual code)
function normalizeServiceType(input) {
    if (!input) return 'residential';

    const inputLower = input.toLowerCase().trim();
    
    // Check exact matches first
    const serviceTypes = {
        'residential_post_construction': 'residential_post_construction',
        'construction': 'construction',
        'office': 'office',
        'residential': 'residential'
    };
    
    if (serviceTypes[input]) {
        return serviceTypes[input];
    }
    
    // Handle construction patterns
    if (inputLower.includes('post') && inputLower.includes('construction')) {
        if (inputLower.includes('commercial') || inputLower.includes('office') || inputLower.includes('business')) {
            return 'construction';
        } else {
            return 'residential_post_construction';
        }
    }
    
    if (inputLower.includes('construction')) {
        return 'construction';
    }
    
    return 'residential';
}

// Price calculation simulation
function calculatePrice(serviceType, propertySize, addOns = []) {
    const basePrices = {
        'residential_post_construction': 250,
        'construction': 300,
        'residential': 120,
        'office': 150
    };
    
    let basePrice = basePrices[serviceType] || 120;
    
    // Size multiplier
    const sizeMultipliers = {
        'small': 0.8,
        'medium': 1.0,
        'large': 1.3,
        'xlarge': 1.6
    };
    
    if (typeof propertySize === 'string') {
        basePrice *= sizeMultipliers[propertySize] || 1.0;
    } else if (typeof propertySize === 'number') {
        basePrice *= Math.max(1, propertySize / 1000);
    }
    
    // Add-on pricing
    const addOnPrices = {
        'dust-removal': 50,
        'window-cleaning': 30,
        'floor-cleaning': 40,
        'deep-sanitization': 100,
        'pressure-washing': 150
    };
    
    let addOnCost = 0;
    addOns.forEach(addOn => {
        addOnCost += addOnPrices[addOn] || 0;
    });
    
    return Math.round(basePrice + addOnCost);
}

// Test 1: Service Type Normalization
async function testServiceTypeNormalization() {
    console.log('\n🔍 Testing Service Type Normalization...');
    
    const testCases = [
        { input: 'residential_post_construction', expected: 'residential_post_construction' },
        { input: 'construction', expected: 'construction' },
        { input: 'post-construction-residential', expected: 'residential_post_construction' },
        { input: 'commercial-construction', expected: 'construction' },
        { input: 'post-construction', expected: 'residential_post_construction' },
        { input: 'renovation-cleanup', expected: 'residential_post_construction' }
    ];
    
    for (const testCase of testCases) {
        const result = normalizeServiceType(testCase.input);
        const passed = result === testCase.expected;
        logTest(
            `Service type normalization: "${testCase.input}" → "${result}"`,
            passed,
            passed ? '' : `Expected "${testCase.expected}", got "${result}"`
        );
    }
}

// Test 2: Price Calculation
async function testPriceCalculation() {
    console.log('\n💰 Testing Price Calculation...');
    
    const testCases = [
        {
            serviceType: 'residential_post_construction',
            propertySize: 'medium',
            addOns: ['dust-removal', 'window-cleaning'],
            expectedMin: 320,
            expectedMax: 380
        },
        {
            serviceType: 'construction',
            propertySize: 5000,
            addOns: ['deep-sanitization', 'pressure-washing'],
            expectedMin: 1450,
            expectedMax: 1550
        }
    ];
    
    for (const testCase of testCases) {
        const calculated = calculatePrice(testCase.serviceType, testCase.propertySize, testCase.addOns);
        const passed = calculated >= testCase.expectedMin && calculated <= testCase.expectedMax;
        logTest(
            `Price calculation for ${testCase.serviceType}: $${calculated}`,
            passed,
            passed ? '' : `Expected $${testCase.expectedMin}-${testCase.expectedMax}, got $${calculated}`
        );
    }
}

// Test 3: Database Schema Validation
async function testDatabaseSchema() {
    console.log('\n🗄️ Testing Database Schema...');
    
    try {
        // Test if booking_forms table exists and has expected structure
        const { data, error } = await supabase
            .from('booking_forms')
            .select('*')
            .limit(1);
        
        if (error && error.code !== 'PGRST116') { // PGRST116 = no data found, which is ok
            logTest('Database connection and table structure', false, error.message);
            return;
        }
        
        logTest('Database connection and table structure', true, 'booking_forms table accessible');
        
        // Test service type constraint
        try {
            const testData = {
                service_type: 'invalid_service_type',
                status: 'pending',
                contact: { test: true },
                property_details: { test: true },
                service_details: { test: true }
            };
            
            const { error: constraintError } = await supabase
                .from('booking_forms')
                .insert([testData]);
            
            if (constraintError && constraintError.message.includes('violates check constraint')) {
                logTest('Service type constraint validation', true, 'Constraint correctly blocks invalid service types');
            } else if (constraintError) {
                logTest('Service type constraint validation', false, `Unexpected error: ${constraintError.message}`);
            } else {
                logTest('Service type constraint validation', false, 'Invalid service type was accepted');
            }
        } catch (error) {
            logTest('Service type constraint validation', false, error.message);
        }
        
    } catch (error) {
        logTest('Database connection', false, error.message);
    }
}

// Test 4: Residential Post-Construction Form Data Flow
async function testResidentialPostConstructionFlow() {
    console.log('\n🏠 Testing Residential Post-Construction Data Flow...');
    
    const testData = generateResidentialPostConstructionData();
    let insertedId = null;
    
    try {
        // Test data insertion
        const { data, error } = await supabase
            .from('booking_forms')
            .insert([testData])
            .select()
            .single();
        
        if (error) {
            logTest('Residential post-construction insertion', false, error.message);
            return;
        }
        
        insertedId = data.id;
        logTest('Residential post-construction insertion', true, `Record created with ID: ${data.id}`);
        
        // Test data retrieval
        const { data: retrieved, error: retrieveError } = await supabase
            .from('booking_forms')
            .select('*')
            .eq('id', data.id)
            .single();
        
        if (retrieveError) {
            logTest('Residential post-construction retrieval', false, retrieveError.message);
            return;
        }
        
        logTest('Residential post-construction retrieval', true, 'Data retrieved successfully');
        
        // Validate service type
        const serviceTypeValid = retrieved.service_type === 'residential_post_construction';
        logTest('Residential service type validation', serviceTypeValid, 
            serviceTypeValid ? '' : `Expected 'residential_post_construction', got '${retrieved.service_type}'`);
        
        // Validate JSONB structure
        const hasValidStructure = retrieved.contact && retrieved.property_details && retrieved.service_details;
        logTest('Residential JSONB structure validation', hasValidStructure, 
            hasValidStructure ? 'All JSONB fields present' : 'Missing JSONB fields');
        
        // Validate contact information
        const contactValid = retrieved.contact.firstName === testData.contact.firstName &&
                           retrieved.contact.email === testData.contact.email;
        logTest('Residential contact data validation', contactValid,
            contactValid ? '' : 'Contact data mismatch');
        
        // Validate service details
        const serviceDetailsValid = retrieved.service_details.constructionType === testData.service_details.constructionType;
        logTest('Residential service details validation', serviceDetailsValid,
            serviceDetailsValid ? '' : 'Service details mismatch');
        
    } catch (error) {
        logTest('Residential post-construction flow', false, error.message);
    } finally {
        // Cleanup
        if (insertedId) {
            await supabase.from('booking_forms').delete().eq('id', insertedId);
        }
    }
}

// Test 5: Commercial Construction Form Data Flow
async function testCommercialConstructionFlow() {
    console.log('\n🏢 Testing Commercial Construction Data Flow...');
    
    const testData = generateCommercialConstructionData();
    let insertedId = null;
    
    try {
        // Test data insertion
        const { data, error } = await supabase
            .from('booking_forms')
            .insert([testData])
            .select()
            .single();
        
        if (error) {
            logTest('Commercial construction insertion', false, error.message);
            return;
        }
        
        insertedId = data.id;
        logTest('Commercial construction insertion', true, `Record created with ID: ${data.id}`);
        
        // Test data retrieval
        const { data: retrieved, error: retrieveError } = await supabase
            .from('booking_forms')
            .select('*')
            .eq('id', data.id)
            .single();
        
        if (retrieveError) {
            logTest('Commercial construction retrieval', false, retrieveError.message);
            return;
        }
        
        logTest('Commercial construction retrieval', true, 'Data retrieved successfully');
        
        // Validate service type
        const serviceTypeValid = retrieved.service_type === 'construction';
        logTest('Commercial service type validation', serviceTypeValid, 
            serviceTypeValid ? '' : `Expected 'construction', got '${retrieved.service_type}'`);
        
        // Validate JSONB structure
        const hasValidStructure = retrieved.contact && retrieved.property_details && retrieved.service_details;
        logTest('Commercial JSONB structure validation', hasValidStructure, 
            hasValidStructure ? 'All JSONB fields present' : 'Missing JSONB fields');
        
        // Validate contact information
        const contactValid = retrieved.contact.companyName === testData.contact.companyName &&
                           retrieved.contact.email === testData.contact.email;
        logTest('Commercial contact data validation', contactValid,
            contactValid ? '' : 'Contact data mismatch');
        
        // Validate project details
        const projectDetailsValid = retrieved.property_details.projectType === testData.property_details.projectType;
        logTest('Commercial project details validation', projectDetailsValid,
            projectDetailsValid ? '' : 'Project details mismatch');
        
        // Validate service package
        const servicePackageValid = retrieved.service_details.servicePackage === testData.service_details.servicePackage;
        logTest('Commercial service package validation', servicePackageValid,
            servicePackageValid ? '' : 'Service package mismatch');
        
    } catch (error) {
        logTest('Commercial construction flow', false, error.message);
    } finally {
        // Cleanup
        if (insertedId) {
            await supabase.from('booking_forms').delete().eq('id', insertedId);
        }
    }
}

// Test 6: Dashboard Data Processing Simulation
async function testDashboardDataProcessing() {
    console.log('\n📊 Testing Dashboard Data Processing...');
    
    // Simulate dashboard data retrieval and processing
    try {
        const { data: existingBookings, error } = await supabase
            .from('booking_forms')
            .select('*')
            .in('service_type', ['residential_post_construction', 'construction'])
            .order('created_at', { ascending: false })
            .limit(10);
        
        if (error) {
            logTest('Dashboard data retrieval', false, error.message);
            return;
        }
        
        logTest('Dashboard data retrieval', true, `Retrieved ${existingBookings.length} construction bookings`);
        
        // Test service type recognition for dashboard display
        let constructionBookingsCount = 0;
        let residentialPostConstructionCount = 0;
        let validBookingsCount = 0;
        
        existingBookings.forEach(booking => {
            if (booking.service_type === 'construction') {
                constructionBookingsCount++;
            }
            if (booking.service_type === 'residential_post_construction') {
                residentialPostConstructionCount++;
            }
            
            // Validate JSONB structure
            if (booking.contact && booking.property_details && booking.service_details) {
                validBookingsCount++;
            }
        });
        
        logTest('Construction service type recognition', 
            constructionBookingsCount >= 0, 
            `Found ${constructionBookingsCount} commercial construction bookings`);
        
        logTest('Residential post-construction recognition', 
            residentialPostConstructionCount >= 0, 
            `Found ${residentialPostConstructionCount} residential post-construction bookings`);
        
        const structureValidationRatio = existingBookings.length > 0 ? 
            validBookingsCount / existingBookings.length : 1;
        
        logTest('JSONB structure validation in existing data', 
            structureValidationRatio >= 0.8, 
            `${validBookingsCount}/${existingBookings.length} bookings have valid JSONB structure (${Math.round(structureValidationRatio * 100)}%)`);
        
    } catch (error) {
        logTest('Dashboard data processing simulation', false, error.message);
    }
}

// Test 7: Error Handling and Edge Cases
async function testErrorHandling() {
    console.log('\n🚨 Testing Error Handling and Edge Cases...');
    
    // Test 1: Missing required fields
    try {
        const incompleteData = {
            service_type: 'residential_post_construction',
            status: 'pending'
            // Missing required fields
        };
        
        const { error } = await supabase
            .from('booking_forms')
            .insert([incompleteData]);
        
        const handlesIncompleteData = error !== null;
        logTest('Incomplete data handling', handlesIncompleteData, 
            handlesIncompleteData ? 'Correctly rejected incomplete data' : 'Unexpectedly accepted incomplete data');
            
    } catch (error) {
        logTest('Incomplete data error handling', true, 'Error correctly caught: ' + error.message);
    }
    
    // Test 2: Invalid service type
    try {
        const invalidServiceTypeData = {
            service_type: 'invalid_construction_type',
            status: 'pending',
            contact: { test: true },
            property_details: { test: true },
            service_details: { test: true }
        };
        
        const { error } = await supabase
            .from('booking_forms')
            .insert([invalidServiceTypeData]);
        
        const rejectsInvalidServiceType = error !== null;
        logTest('Invalid service type handling', rejectsInvalidServiceType, 
            rejectsInvalidServiceType ? 'Correctly rejected invalid service type' : 'Unexpectedly accepted invalid service type');
            
    } catch (error) {
        logTest('Invalid service type error handling', true, 'Error correctly caught: ' + error.message);
    }
    
    // Test 3: Malformed JSONB data handling
    const normalizedServiceType = normalizeServiceType('malformed-construction-type');
    const fallbackHandled = normalizedServiceType !== 'malformed-construction-type';
    logTest('Malformed service type fallback', fallbackHandled, 
        fallbackHandled ? `Fallback to: ${normalizedServiceType}` : 'No fallback applied');
}

// Test 8: Performance and Load Testing
async function testPerformanceMetrics() {
    console.log('\n⚡ Testing Performance Metrics...');
    
    const startTime = performance.now();
    
    // Test concurrent operations
    const concurrentPromises = [];
    const testCount = 5;
    
    for (let i = 0; i < testCount; i++) {
        concurrentPromises.push(
            supabase
                .from('booking_forms')
                .select('id, service_type, created_at')
                .in('service_type', ['residential_post_construction', 'construction'])
                .limit(1)
        );
    }
    
    try {
        const results = await Promise.all(concurrentPromises);
        const endTime = performance.now();
        const totalTime = endTime - startTime;
        const averageTime = totalTime / testCount;
        
        const allSuccessful = results.every(result => !result.error);
        logTest('Concurrent query performance', allSuccessful, 
            `${testCount} concurrent queries completed in ${totalTime.toFixed(2)}ms (avg: ${averageTime.toFixed(2)}ms per query)`);
        
        const performanceAcceptable = averageTime < 500; // 500ms threshold
        logTest('Performance threshold check', performanceAcceptable, 
            performanceAcceptable ? 'Performance within acceptable limits' : `Average query time ${averageTime.toFixed(2)}ms exceeds 500ms threshold`);
            
    } catch (error) {
        logTest('Concurrent query performance', false, error.message);
    }
}

// Main test runner
async function runAllTests() {
    console.log('🚀 Starting Comprehensive Construction Form Integration Tests...\n');
    
    const startTime = performance.now();
    
    try {
        await testServiceTypeNormalization();
        await testPriceCalculation();
        await testDatabaseSchema();
        await testResidentialPostConstructionFlow();
        await testCommercialConstructionFlow();
        await testDashboardDataProcessing();
        await testErrorHandling();
        await testPerformanceMetrics();
        
    } catch (error) {
        console.error('\n❌ CRITICAL ERROR during test execution:', error);
        testResults.failed++;
        testResults.total++;
    }
    
    const endTime = performance.now();
    const totalTime = (endTime - startTime) / 1000;
    
    // Print comprehensive results
    console.log('\n' + '='.repeat(60));
    console.log('📊 COMPREHENSIVE TEST RESULTS');
    console.log('='.repeat(60));
    console.log(`⏱️  Total execution time: ${totalTime.toFixed(2)} seconds`);
    console.log(`📋 Tests executed: ${testResults.total}`);
    console.log(`✅ Tests passed: ${testResults.passed}`);
    console.log(`❌ Tests failed: ${testResults.failed}`);
    console.log(`📈 Success rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
    
    if (testResults.failed > 0) {
        console.log('\n⚠️  FAILED TESTS SUMMARY:');
        testResults.tests
            .filter(test => !test.passed)
            .forEach(test => {
                console.log(`   ❌ ${test.name}${test.details ? ': ' + test.details : ''}`);
            });
    }
    
    console.log('\n🏆 TEST CATEGORIES BREAKDOWN:');
    const categories = {
        'Service Type Processing': testResults.tests.filter(t => t.name.includes('Service type') || t.name.includes('normalization')),
        'Database Operations': testResults.tests.filter(t => t.name.includes('insertion') || t.name.includes('retrieval') || t.name.includes('Database')),
        'Data Validation': testResults.tests.filter(t => t.name.includes('validation') || t.name.includes('structure')),
        'Error Handling': testResults.tests.filter(t => t.name.includes('error') || t.name.includes('handling') || t.name.includes('Invalid')),
        'Performance': testResults.tests.filter(t => t.name.includes('performance') || t.name.includes('concurrent'))
    };
    
    Object.entries(categories).forEach(([category, tests]) => {
        if (tests.length > 0) {
            const passed = tests.filter(t => t.passed).length;
            const rate = ((passed / tests.length) * 100).toFixed(1);
            const status = rate === '100.0' ? '✅' : rate >= '80.0' ? '⚠️' : '❌';
            console.log(`   ${status} ${category}: ${passed}/${tests.length} (${rate}%)`);
        }
    });
    
    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    
    if (testResults.passed === testResults.total) {
        console.log('   🎉 All tests passed! The construction form integration is working correctly.');
    } else {
        console.log('   🔧 Issues detected. Please review failed tests and implement fixes.');
        
        if (testResults.tests.some(t => !t.passed && t.name.includes('service type'))) {
            console.log('   📝 Service type normalization needs attention.');
        }
        if (testResults.tests.some(t => !t.passed && t.name.includes('insertion'))) {
            console.log('   🗄️ Database schema or constraints may need updates.');
        }
        if (testResults.tests.some(t => !t.passed && t.name.includes('validation'))) {
            console.log('   ✅ Data validation logic requires improvement.');
        }
        if (testResults.tests.some(t => !t.passed && t.name.includes('performance'))) {
            console.log('   ⚡ Performance optimization recommended.');
        }
    }
    
    console.log('\n🎯 NEXT STEPS:');
    console.log('   1. Review any failed tests and implement fixes');
    console.log('   2. Test the forms manually in the browser');
    console.log('   3. Monitor console logs for debugging information');
    console.log('   4. Verify dashboard display of construction bookings');
    console.log('   5. Test payment integration with construction forms');
    
    console.log('\n' + '='.repeat(60));
    
    // Exit with appropriate code
    process.exit(testResults.failed === 0 ? 0 : 1);
}

// Run tests
runAllTests().catch(error => {
    console.error('❌ Fatal error running tests:', error);
    process.exit(1);
});